const mongoose = require('mongoose');

// Connexion à MongoDB
mongoose.connect('mongodb://localhost:27017/emba_db')
  .then(async () => {
    console.log('✅ Connecté à MongoDB');
    
    // Importer les modèles
    const User = require('./models/User');
    const Student = require('./models/Student');
    const Professor = require('./models/Professor');
    const Application = require('./models/Application');
    
    console.log('\n=== STATISTIQUES BASE DE DONNÉES ===');
    const userCount = await User.countDocuments();
    const studentCount = await Student.countDocuments();
    const professorCount = await Professor.countDocuments();
    const applicationCount = await Application.countDocuments();
    
    console.log(`Users: ${userCount}`);
    console.log(`Students: ${studentCount}`);
    console.log(`Professors: ${professorCount}`);
    console.log(`Applications: ${applicationCount}`);
    
    console.log('\n=== DÉTAIL USERS ===');
    const users = await User.find({}, 'firstName lastName email role').limit(10);
    users.forEach(u => console.log(`${u.firstName} ${u.lastName} (${u.email}) - ${u.role}`));
    
    console.log('\n=== DÉTAIL STUDENTS ===');
    const students = await Student.find({}).populate('user', 'firstName lastName email').limit(10);
    students.forEach(s => console.log(`${s.studentNumber} - ${s.user?.firstName} ${s.user?.lastName}`));
    
    console.log('\n=== DÉTAIL PROFESSORS ===');
    const professors = await Professor.find({}).populate('user', 'firstName lastName email').limit(10);
    professors.forEach(p => console.log(`${p.employeeId || 'No ID'} - ${p.user?.firstName} ${p.user?.lastName}`));
    
    console.log('\n=== APPLICATIONS ACCEPTÉES ===');
    const acceptedApps = await Application.find({ 'applicationStatus.status': 'accepted' });
    console.log(`Applications acceptées: ${acceptedApps.length}`);
    acceptedApps.forEach(app => console.log(`${app.personalInfo.firstName} ${app.personalInfo.lastName} - Compte créé: ${app.applicationStatus.studentAccountCreated}`));
    
    console.log('\n=== USERS PAR RÔLE ===');
    const usersByRole = await User.aggregate([
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);
    usersByRole.forEach(role => console.log(`${role._id}: ${role.count}`));
    
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Erreur de connexion:', error);
    process.exit(1);
  });
