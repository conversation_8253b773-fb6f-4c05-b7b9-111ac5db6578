const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class YouSignService {
  constructor() {
    this.BASE_URL = process.env.YOUSIGN_API_URL || 'https://api-sandbox.yousign.app/v3';
    this.API_KEY = process.env.YOUSIGN_API_KEY || 'YOUR_YOUSIGN_API_KEY';
  }

  async request(endpoint = '', options = {}, headers = {}) {
    const url = `${this.BASE_URL}/${endpoint}`;
    const config = {
      url,
      headers: {
        Authorization: `Bearer ${this.API_KEY}`,
        ...headers
      },
      ...options
    };

    try {
      const res = await axios(config);
      return res.data;
    } catch (error) {
      console.error('❌ Erreur API YouSign:', error.response?.data || error.message);
      throw new Error(`Erreur API YouSign: ${error.response?.data?.message || error.message}`);
    }
  }

  // Initier une demande de signature pour une candidature EMBA
  async initiateApplicationSignature(applicationData) {
    const body = {
      name: `Candidature EMBA - ${applicationData.nom} ${applicationData.prenom}`,
      delivery_mode: 'email',
      timezone: 'Europe/Paris',
      description: `Signature de la candidature EMBA pour ${applicationData.nom} ${applicationData.prenom}`,
      metadata: {
        application_id: applicationData.applicationId,
        candidate_email: applicationData.email,
        candidate_name: `${applicationData.nom} ${applicationData.prenom}`
      }
    };

    const options = {
      method: 'POST',
      data: JSON.stringify(body),
    };

    const headers = {
      'Content-type': 'application/json',
    };

    return this.request('signature_requests', options, headers);
  }

  // Uploader le document de candidature à signer
  async uploadApplicationDocument(signatureRequestId, documentPath, documentName) {
    const form = new FormData();
    form.append('file', fs.createReadStream(documentPath), {
      filename: documentName,
    });
    form.append('nature', 'signable_document');
    form.append('parse_anchors', 'true');

    const options = {
      method: 'POST',
      data: form,
    };

    const headers = form.getHeaders();
    return this.request(`signature_requests/${signatureRequestId}/documents`, options, headers);
  }

  // Ajouter le candidat comme signataire
  async addCandidateSigner(signatureRequestId, documentId, candidateInfo) {
    const body = {
      info: {
        first_name: candidateInfo.prenom,
        last_name: candidateInfo.nom,
        email: candidateInfo.email,
        phone_number: candidateInfo.telPortable || candidateInfo.telephone,
        locale: 'fr',
      },
      signature_level: 'electronic_signature',
      signature_authentication_mode: 'otp_email', // Authentification par email OTP
      fields: [
        {
          document_id: documentId,
          type: 'signature',
          page: 1,
          x: 400, // Position X de la signature
          y: 100, // Position Y de la signature
          width: 150,
          height: 50
        }
      ]
    };

    const options = {
      method: 'POST',
      data: JSON.stringify(body),
    };

    const headers = {
      'Content-type': 'application/json',
    };

    return this.request(`signature_requests/${signatureRequestId}/signers`, options, headers);
  }

  // Activer la demande de signature
  async activateSignatureRequest(signatureRequestId) {
    const options = {
      method: 'POST',
    };
    return this.request(`signature_requests/${signatureRequestId}/activate`, options);
  }

  // Obtenir le statut d'une demande de signature
  async getSignatureRequestStatus(signatureRequestId) {
    const options = {
      method: 'GET',
    };
    return this.request(`signature_requests/${signatureRequestId}`, options);
  }

  // Télécharger le document signé
  async downloadSignedDocument(signatureRequestId, documentId) {
    const options = {
      method: 'GET',
      responseType: 'stream'
    };
    return this.request(`signature_requests/${signatureRequestId}/documents/${documentId}/download`, options);
  }

  // Processus complet de signature pour une candidature EMBA
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🔏 Début du processus de signature YouSign pour:', applicationData.email);

      // 1. Initier la demande de signature
      const signatureRequest = await this.initiateApplicationSignature(applicationData);
      console.log('✅ Demande de signature créée:', signatureRequest.id);

      // 2. Uploader le document
      const documentName = `Candidature_EMBA_${applicationData.nom}_${applicationData.prenom}.pdf`;
      const document = await this.uploadApplicationDocument(
        signatureRequest.id, 
        documentPath, 
        documentName
      );
      console.log('✅ Document uploadé:', document.id);

      // 3. Ajouter le candidat comme signataire
      const signer = await this.addCandidateSigner(
        signatureRequest.id, 
        document.id, 
        applicationData
      );
      console.log('✅ Signataire ajouté:', signer.id);

      // 4. Activer la demande de signature
      await this.activateSignatureRequest(signatureRequest.id);
      console.log('✅ Demande de signature activée');

      return {
        success: true,
        signatureRequestId: signatureRequest.id,
        documentId: document.id,
        signerId: signer.id,
        message: 'Demande de signature envoyée avec succès'
      };

    } catch (error) {
      console.error('❌ Erreur lors du processus de signature:', error);
      return {
        success: false,
        error: error.message,
        message: 'Erreur lors de l\'envoi de la demande de signature'
      };
    }
  }
}

module.exports = new YouSignService();
