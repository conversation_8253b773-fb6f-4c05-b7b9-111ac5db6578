const nodemailer = require('nodemailer');

// Configuration du transporteur email
const createTransporter = () => {
  try {
    // Configuration utilisant les variables d'environnement
    const config = {
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT) || 587,
      secure: process.env.EMAIL_SECURE === 'true', // true pour 465, false pour autres ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      },
      // Options supplémentaires pour Gmail
      tls: {
        rejectUnauthorized: false
      }
    };

    // Vérifier que les credentials sont configurés
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      console.warn('⚠️ Variables d\'environnement EMAIL_USER et EMAIL_PASS non configurées');
      console.warn('📧 Les emails ne seront pas envoyés');
      return null;
    }

    console.log('📧 Configuration email:', {
      host: config.host,
      port: config.port,
      user: config.auth.user,
      secure: config.secure
    });

    return nodemailer.createTransport(config);
  } catch (error) {
    console.error('❌ Erreur lors de la création du transporteur email:', error);
    return null;
  }
};

// Template d'email pour les identifiants étudiants
const getStudentCredentialsTemplate = (studentData) => {
  return {
    subject: 'Bienvenue dans le programme EMBA - Vos identifiants de connexion',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .credentials { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎓 Bienvenue dans le programme EMBA</h1>
            <p>Félicitations ! Votre candidature a été acceptée</p>
          </div>
          
          <div class="content">
            <h2>Bonjour ${studentData.firstName} ${studentData.lastName},</h2>
            
            <p>Nous avons le plaisir de vous informer que votre candidature au programme Executive MBA a été <strong>acceptée</strong> !</p>
            
            <p>Votre compte étudiant a été créé avec succès. Voici vos identifiants de connexion :</p>
            
            <div class="credentials">
              <h3>📧 Identifiants de connexion</h3>
              <p><strong>Numéro d'étudiant :</strong> ${studentData.studentNumber}</p>
              <p><strong>Email :</strong> ${studentData.email}</p>
              <p><strong>Mot de passe temporaire :</strong> ${studentData.temporaryPassword}</p>
            </div>
            
            <p><strong>⚠️ Important :</strong> Pour des raisons de sécurité, veuillez changer votre mot de passe lors de votre première connexion.</p>
            
            <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/login" class="button">
              Se connecter à la plateforme
            </a>
            
            <h3>📋 Prochaines étapes :</h3>
            <ul>
              <li>Connectez-vous à votre espace étudiant</li>
              <li>Complétez votre profil</li>
              <li>Consultez votre planning de cours</li>
              <li>Téléchargez les documents nécessaires</li>
            </ul>
            
            <p>Si vous avez des questions, n'hésitez pas à nous contacter à <a href="mailto:<EMAIL>"><EMAIL></a></p>
            
            <p>Nous vous souhaitons une excellente formation !</p>
            
            <p>Cordialement,<br>
            <strong>L'équipe EMBA ESPRIT</strong></p>
          </div>
          
          <div class="footer">
            <p>© 2024 EMBA ESPRIT - Tous droits réservés</p>
            <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Template d'email pour candidature refusée
const getRejectionTemplate = (candidateData, reason) => {
  return {
    subject: 'Candidature EMBA - Décision',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f3f4f6; color: #374151; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Candidature EMBA</h1>
          </div>
          
          <div class="content">
            <h2>Bonjour ${candidateData.firstName} ${candidateData.lastName},</h2>
            
            <p>Nous vous remercions pour l'intérêt que vous avez porté à notre programme Executive MBA.</p>
            
            <p>Après un examen attentif de votre dossier, nous regrettons de vous informer que nous ne pouvons pas donner suite favorable à votre candidature pour cette session.</p>
            
            ${reason ? `<p><strong>Motif :</strong> ${reason}</p>` : ''}
            
            <p>Cette décision ne remet pas en question vos qualifications professionnelles. Nous vous encourageons à postuler à nouveau lors des prochaines sessions d'admission.</p>
            
            <p>Nous vous souhaitons beaucoup de succès dans vos projets futurs.</p>
            
            <p>Cordialement,<br>
            <strong>L'équipe EMBA ESPRIT</strong></p>
          </div>
          
          <div class="footer">
            <p>© 2024 EMBA ESPRIT - Tous droits réservés</p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Template d'email pour entretien programmé
const getInterviewTemplate = (candidateData, interviewDate) => {
  return {
    subject: 'Candidature EMBA - Entretien programmé',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .interview-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎯 Entretien EMBA</h1>
            <p>Votre candidature progresse !</p>
          </div>
          
          <div class="content">
            <h2>Bonjour ${candidateData.firstName} ${candidateData.lastName},</h2>
            
            <p>Nous avons le plaisir de vous informer que votre candidature au programme Executive MBA a retenu notre attention.</p>
            
            <p>Nous souhaitons vous rencontrer lors d'un entretien pour mieux vous connaître et discuter de votre projet professionnel.</p>
            
            <div class="interview-info">
              <h3>📅 Informations sur l'entretien</h3>
              <p><strong>Date :</strong> ${interviewDate || 'À définir'}</p>
              <p><strong>Durée :</strong> Environ 45 minutes</p>
              <p><strong>Format :</strong> Entretien individuel</p>
              <p><strong>Lieu :</strong> Campus ESPRIT ou en visioconférence</p>
            </div>
            
            <h3>📋 Préparation de l'entretien :</h3>
            <ul>
              <li>Préparez une présentation de votre parcours professionnel</li>
              <li>Réfléchissez à vos motivations pour intégrer l'EMBA</li>
              <li>Préparez vos questions sur le programme</li>
              <li>Apportez une copie de votre CV et de vos diplômes</li>
            </ul>
            
            <p>Un membre de notre équipe vous contactera prochainement pour confirmer les détails pratiques de cet entretien.</p>
            
            <p>Nous nous réjouissons de vous rencontrer !</p>
            
            <p>Cordialement,<br>
            <strong>L'équipe EMBA ESPRIT</strong></p>
          </div>
          
          <div class="footer">
            <p>© 2024 EMBA ESPRIT - Tous droits réservés</p>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Fonction pour envoyer un email
const sendEmail = async (to, template) => {
  try {
    const transporter = createTransporter();

    // Si le transporteur n'est pas disponible, simuler l'envoi
    if (!transporter) {
      console.warn('⚠️ Transporteur email non disponible - Simulation d\'envoi');
      console.log('📧 Email simulé envoyé à:', to);
      console.log('📧 Sujet:', template.subject);
      return {
        success: true,
        messageId: 'simulated-' + Date.now(),
        simulated: true
      };
    }

    const mailOptions = {
      from: `"EMBA ESPRIT" <${process.env.EMAIL_USER || process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: to,
      subject: template.subject,
      html: template.html
    };

    console.log('📧 Tentative d\'envoi d\'email à:', to);
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email envoyé avec succès:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi de l\'email:', error);
    console.error('📧 Détails de l\'erreur:', {
      code: error.code,
      command: error.command,
      response: error.response
    });

    // En cas d'erreur, on considère que l'email est "envoyé" pour ne pas bloquer le processus
    console.warn('⚠️ Email non envoyé mais processus continué');
    return {
      success: true,
      error: error.message,
      fallback: true
    };
  }
};

// Template d'email pour le code de confirmation de changement de mot de passe
const getPasswordResetCodeTemplate = (data) => {
  return {
    subject: 'Code de confirmation - Changement de mot de passe EMBA',
    html: `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Code de Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #b91c1c); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
          .code-box { background: #fff; border: 2px solid #dc2626; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0; }
          .code { font-size: 32px; font-weight: bold; color: #dc2626; letter-spacing: 5px; font-family: monospace; }
          .warning { background: #fef3c7; border-left: 4px solid #f59e0b; padding: 15px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #6b7280; font-size: 14px; }
          .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔐 Code de Confirmation</h1>
            <p>Changement de mot de passe EMBA</p>
          </div>

          <div class="content">
            <h2>Bonjour,</h2>

            <p>Vous avez demandé à changer votre mot de passe pour votre compte EMBA. Pour des raisons de sécurité, veuillez utiliser le code de confirmation ci-dessous :</p>

            <div class="code-box">
              <div class="code">${data.confirmationCode}</div>
              <p style="margin: 10px 0 0 0; color: #6b7280;">Code de confirmation</p>
            </div>

            <div class="warning">
              <strong>⚠️ Important :</strong>
              <ul style="margin: 10px 0;">
                <li>Ce code expire dans <strong>15 minutes</strong></li>
                <li>Ne partagez jamais ce code avec personne</li>
                <li>Si vous n'avez pas demandé ce changement, ignorez cet email</li>
              </ul>
            </div>

            <h3>Comment utiliser ce code :</h3>
            <ol>
              <li>Retournez sur la page de changement de mot de passe</li>
              <li>Saisissez ce code de confirmation</li>
              <li>Entrez votre nouveau mot de passe</li>
              <li>Confirmez le changement</li>
            </ol>

            <p>Si vous rencontrez des difficultés, contactez l'administration EMBA.</p>

            <div class="footer">
              <p>Cet email a été envoyé automatiquement par le système EMBA ESPRIT.</p>
              <p>© ${new Date().getFullYear()} EMBA ESPRIT - Tous droits réservés</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  };
};

// Fonctions spécialisées
const sendStudentCredentials = async (studentData) => {
  const template = getStudentCredentialsTemplate(studentData);
  return await sendEmail(studentData.email, template);
};

const sendPasswordResetCode = async (data) => {
  const template = getPasswordResetCodeTemplate(data);
  return await sendEmail(data.email, template);
};

const sendRejectionEmail = async (candidateData, reason) => {
  const template = getRejectionTemplate(candidateData, reason);
  return await sendEmail(candidateData.email, template);
};

const sendInterviewEmail = async (candidateData, interviewDate) => {
  const template = getInterviewTemplate(candidateData, interviewDate);
  return await sendEmail(candidateData.email, template);
};

module.exports = {
  sendEmail,
  sendStudentCredentials,
  sendPasswordResetCode,
  sendRejectionEmail,
  sendInterviewEmail,
  getStudentCredentialsTemplate,
  getPasswordResetCodeTemplate,
  getRejectionTemplate,
  getInterviewTemplate
};
