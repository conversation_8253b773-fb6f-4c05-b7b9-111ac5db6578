const express = require('express');
const router = express.Router();
const Course = require('../models/Course');
const Professor = require('../models/Professor');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/courses - Obtenir tous les cours avec filtres pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('📚 Récupération des cours avec filtres:', req.query);

    const { page = 1, limit = 50, academicYear, semester, status, search } = req.query;
    const query = {};

    // Appliquer les filtres
    if (academicYear) query.academicYear = academicYear;
    if (semester) query.semester = semester;
    if (status) query.status = status;

    // Recherche textuelle
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { courseCode: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const courses = await Course.find(query)
      .populate('instructor', 'user academicTitle department')
      .populate('coInstructors', 'user academicTitle department')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Course.countDocuments(query);

    // Enrichir les données pour l'interface admin
    const enrichedCourses = await Promise.all(courses.map(async (course) => {
      const enrolledStudents = await course.getEnrolledStudents();

      return {
        id: course._id,
        code: course.courseCode,
        title: course.title,
        description: course.description,
        credits: course.credits,
        hours: course.totalHours,
        semester: course.semester,
        status: course.status || 'active',
        professor: course.instructor ? {
          id: course.instructor._id,
          name: course.instructor.user ?
            `${course.instructor.user.firstName} ${course.instructor.user.lastName}` :
            'Nom non disponible',
          email: course.instructor.user?.email || 'Email non disponible'
        } : null,
        enrolledStudents: enrolledStudents ? enrolledStudents.length : 0,
        maxStudents: course.maxStudents || 20,
        startDate: course.startDate,
        endDate: course.endDate,
        schedule: course.schedule || []
      };
    }));

    console.log(`✅ ${enrichedCourses.length} cours récupérés`);

    res.json({
      success: true,
      courses: enrichedCourses,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des cours:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des cours',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/courses/:id
router.get('/:id', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id)
      .populate('instructor', 'user academicTitle department')
      .populate('coInstructors', 'user academicTitle department')
      .populate('prerequisites.course', 'courseCode title');
    
    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }
    
    res.json(course);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/courses
router.post('/', async (req, res) => {
  try {
    const course = new Course(req.body);
    await course.save();
    
    const populatedCourse = await Course.findById(course._id)
      .populate('instructor', 'user academicTitle department');
    
    res.status(201).json(populatedCourse);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// PUT /api/courses/:id
router.put('/:id', async (req, res) => {
  try {
    const course = await Course.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.body.updatedBy },
      { new: true, runValidators: true }
    ).populate('instructor', 'user academicTitle department');
    
    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }
    
    res.json(course);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/courses/:id/students
router.get('/:id/students', async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    
    if (!course) {
      return res.status(404).json({ message: 'Course not found' });
    }
    
    const students = await course.getEnrolledStudents();
    res.json(students);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
