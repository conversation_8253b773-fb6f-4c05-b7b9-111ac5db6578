import React, { useState } from 'react';
import {
  StarIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const InterviewEvaluation = ({ interview, onSubmit, onCancel }) => {
  const [evaluation, setEvaluation] = useState({
    scores: {
      communication: { score: 5, comments: '' },
      leadership: { score: 5, comments: '' },
      motivation: { score: 5, comments: '' },
      experience: { score: 5, comments: '' },
      fitForProgram: { score: 5, comments: '' },
      overallImpression: { score: 5, comments: '' }
    },
    recommendation: 'neutral',
    strengths: [''],
    concerns: [''],
    generalComments: '',
    questionsAsked: [''],
    candidateResponses: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const criteriaLabels = {
    communication: 'Communication',
    leadership: 'Leadership',
    motivation: 'Motivation',
    experience: 'Expérience professionnelle',
    fitForProgram: 'Adéquation au programme',
    overallImpression: 'Impression générale'
  };

  const recommendationOptions = [
    { value: 'strongly_recommend', label: 'Fortement recommandé', color: 'green' },
    { value: 'recommend', label: 'Recommandé', color: 'blue' },
    { value: 'neutral', label: 'Neutre', color: 'yellow' },
    { value: 'not_recommend', label: 'Non recommandé', color: 'orange' },
    { value: 'strongly_not_recommend', label: 'Fortement non recommandé', color: 'red' }
  ];

  const updateScore = (criteria, score) => {
    setEvaluation(prev => ({
      ...prev,
      scores: {
        ...prev.scores,
        [criteria]: {
          ...prev.scores[criteria],
          score
        }
      }
    }));
  };

  const updateComments = (criteria, comments) => {
    setEvaluation(prev => ({
      ...prev,
      scores: {
        ...prev.scores,
        [criteria]: {
          ...prev.scores[criteria],
          comments
        }
      }
    }));
  };

  const updateArrayField = (field, index, value) => {
    setEvaluation(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setEvaluation(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setEvaluation(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit(evaluation);
    } catch (error) {
      console.error('Erreur soumission évaluation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const StarRating = ({ score, onChange }) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onChange(star)}
            className="focus:outline-none"
          >
            {star <= score ? (
              <StarIconSolid className="h-5 w-5 text-yellow-400" />
            ) : (
              <StarIcon className="h-5 w-5 text-gray-300" />
            )}
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
          {score}/10
        </span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Informations de l'entretien */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Évaluation d'entretien
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Candidat:</span>
            <p className="text-gray-900 dark:text-white">
              {interview.application?.personalInfo?.firstName} {interview.application?.personalInfo?.lastName}
            </p>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Date:</span>
            <p className="text-gray-900 dark:text-white">
              {new Date(interview.scheduledDate).toLocaleDateString('fr-FR', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Grille d'évaluation */}
      <div>
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Grille d'évaluation
        </h4>
        <div className="space-y-6">
          {Object.entries(criteriaLabels).map(([key, label]) => (
            <div key={key} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {label}
                </label>
                <StarRating
                  score={evaluation.scores[key].score}
                  onChange={(score) => updateScore(key, score)}
                />
              </div>
              <textarea
                value={evaluation.scores[key].comments}
                onChange={(e) => updateComments(key, e.target.value)}
                placeholder={`Commentaires sur ${label.toLowerCase()}...`}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Recommandation */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Recommandation finale
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {recommendationOptions.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => setEvaluation(prev => ({...prev, recommendation: option.value}))}
              className={`p-3 rounded-lg border-2 text-sm font-medium transition-colors ${
                evaluation.recommendation === option.value
                  ? `border-${option.color}-500 bg-${option.color}-50 text-${option.color}-700`
                  : 'border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Points forts */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Points forts
        </label>
        {evaluation.strengths.map((strength, index) => (
          <div key={index} className="flex items-center space-x-2 mb-2">
            <input
              type="text"
              value={strength}
              onChange={(e) => updateArrayField('strengths', index, e.target.value)}
              placeholder="Point fort..."
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            />
            <button
              type="button"
              onClick={() => removeArrayItem('strengths', index)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayItem('strengths')}
          className="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300"
        >
          + Ajouter un point fort
        </button>
      </div>

      {/* Préoccupations */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Préoccupations
        </label>
        {evaluation.concerns.map((concern, index) => (
          <div key={index} className="flex items-center space-x-2 mb-2">
            <input
              type="text"
              value={concern}
              onChange={(e) => updateArrayField('concerns', index, e.target.value)}
              placeholder="Préoccupation..."
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            />
            <button
              type="button"
              onClick={() => removeArrayItem('concerns', index)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        ))}
        <button
          type="button"
          onClick={() => addArrayItem('concerns')}
          className="text-sm text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-300"
        >
          + Ajouter une préoccupation
        </button>
      </div>

      {/* Commentaires généraux */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Commentaires généraux
        </label>
        <textarea
          value={evaluation.generalComments}
          onChange={(e) => setEvaluation(prev => ({...prev, generalComments: e.target.value}))}
          placeholder="Commentaires généraux sur l'entretien..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>

      {/* Boutons d'action */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
        >
          Annuler
        </button>
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Enregistrement...
            </>
          ) : (
            <>
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Enregistrer l'évaluation
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default InterviewEvaluation;
