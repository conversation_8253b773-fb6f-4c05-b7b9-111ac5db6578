import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';

// Composants publics
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Contact from './pages/Contact';
import Apply from './pages/Apply';
import PublicLogin from './pages/PublicLogin';

// Composants d'administration
import Layout from './components/Layout/Layout';
import ProtectedRoute, { PublicRoute } from './components/ProtectedRoute';
import Dashboard from './pages/Dashboard';
import Students from './pages/Students';
import Applications from './pages/Applications';
import StudentDashboard from './pages/StudentDashboard';
import ProfessorList from './components/admin/ProfessorList';
import ProfessorDashboard from './pages/ProfessorDashboard';
import ProfessorProfile from './components/professor/ProfessorProfile';
import AdminCourses from './components/admin/AdminCourses';
import AdminPayments from './components/admin/AdminPayments';
import AdminNotifications from './components/admin/AdminNotifications';
import AdminDocuments from './components/admin/AdminDocuments';
import AdminSchedule from './components/admin/AdminSchedule';
import AdminGrades from './components/admin/AdminGrades';
import AdminSettings from './components/admin/AdminSettings';


// Composant wrapper pour les routes
const AppContent = () => {
  return (
    <Router>
        <Routes>
          {/* Page d'accueil publique */}
          <Route
            path="/"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <Home />
                </main>
                <Footer />
              </div>
            }
          />

          {/* Autres pages publiques */}
          <Route
            path="/about"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <About />
                </main>
                <Footer />
              </div>
            }
          />
          <Route
            path="/contact"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <Contact />
                </main>
                <Footer />
              </div>
            }
          />
          <Route
            path="/apply"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <Apply />
                </main>
                <Footer />
              </div>
            }
          />

          {/* Routes publiques */}
          <Route
            path="/login"
            element={
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-grow">
                  <PublicLogin />
                </main>
                <Footer />
              </div>
            }
          />
          <Route
            path="/admin/login"
            element={
              <PublicRoute>
                <div className="min-h-screen flex flex-col">
                  <Header />
                  <main className="flex-grow">
                    <PublicLogin />
                  </main>
                  <Footer />
                </div>
              </PublicRoute>
            }
          />

          {/* Student Dashboard Route */}
          <Route
            path="/student/dashboard"
            element={
              <ProtectedRoute requiredRole="student">
                <StudentDashboard />
              </ProtectedRoute>
            }
          />

          {/* Routes d'administration protégées */}
          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/dashboard"
            element={
              <ProtectedRoute>
                <Layout>
                  <Dashboard />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/students"
            element={
              <ProtectedRoute>
                <Layout>
                  <Students />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/applications"
            element={
              <ProtectedRoute>
                <Layout>
                  <Applications />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/professors"
            element={
              <ProtectedRoute>
                <Layout>
                  <ProfessorList />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/courses"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminCourses />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/payments"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminPayments />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/notifications"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminNotifications />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/documents"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminDocuments />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/schedule"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminSchedule />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/grades"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminGrades />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/settings"
            element={
              <ProtectedRoute>
                <Layout>
                  <AdminSettings />
                </Layout>
              </ProtectedRoute>
            }
          />

          {/* Routes pour les professeurs */}
          <Route
            path="/professor/dashboard"
            element={
              <ProtectedRoute>
                <Layout>
                  <ProfessorDashboard />
                </Layout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/professor/profile"
            element={
              <ProtectedRoute>
                <Layout>
                  <ProfessorProfile />
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
    </Router>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
