const express = require('express');
const router = express.Router();
const Schedule = require('../models/Schedule');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/schedules/events - Obtenir les événements pour l'interface admin
router.get('/events', authenticate, async (req, res) => {
  try {
    console.log('📅 Récupération des événements avec filtres:', req.query);

    const { date, roomId, type, page = 1, limit = 50 } = req.query;
    const query = {};

    // Appliquer les filtres
    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);
      query.date = { $gte: startDate, $lt: endDate };
    }
    if (roomId) query.room = roomId;
    if (type) query.type = type;

    const schedules = await Schedule.find(query)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('module', 'moduleCode title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ date: 1, startTime: 1 });

    const total = await Schedule.countDocuments(query);

    // Enrichir les données pour l'interface admin
    const enrichedEvents = schedules.map(schedule => ({
      id: schedule._id,
      title: schedule.course?.title || schedule.module?.title || 'Événement',
      type: schedule.type || 'course',
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      date: schedule.date.toISOString().split('T')[0],
      room: {
        id: schedule.room,
        name: schedule.roomName || `Salle ${schedule.room}`
      },
      professor: schedule.instructor ? {
        id: schedule.instructor._id,
        name: schedule.instructor.user ?
          `${schedule.instructor.user.firstName} ${schedule.instructor.user.lastName}` :
          'Professeur inconnu',
        email: schedule.instructor.user?.email || ''
      } : null,
      students: schedule.enrolledStudents || 0,
      maxStudents: schedule.maxStudents || 20,
      status: schedule.status || 'confirmed',
      description: schedule.description || '',
      color: getEventColor(schedule.type)
    }));

    console.log(`✅ ${enrichedEvents.length} événements récupérés`);

    res.json({
      success: true,
      events: enrichedEvents,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des événements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des événements',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Fonction utilitaire pour obtenir la couleur d'un événement
function getEventColor(type) {
  const colors = {
    course: 'blue',
    exam: 'red',
    meeting: 'purple',
    maintenance: 'orange'
  };
  return colors[type] || 'blue';
}

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, date, instructor, course } = req.query;
    const query = {};
    if (date) query.date = { $gte: new Date(date) };
    if (instructor) query.instructor = instructor;
    if (course) query.course = course;
    
    const schedules = await Schedule.find(query)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('module', 'moduleCode title')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ date: 1, startTime: 1 });
    
    const total = await Schedule.countDocuments(query);
    res.json({ schedules, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const schedule = await Schedule.findById(req.params.id)
      .populate('instructor', 'user academicTitle')
      .populate('course', 'courseCode title')
      .populate('participants.expectedStudents', 'studentNumber user');
    
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    res.json(schedule);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const schedule = new Schedule(req.body);
    await schedule.save();
    res.status(201).json(schedule);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const schedule = await Schedule.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    res.json(schedule);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/attendance', async (req, res) => {
  try {
    const { studentId, status, recordedBy, notes } = req.body;
    const schedule = await Schedule.findById(req.params.id);
    
    if (!schedule) return res.status(404).json({ message: 'Schedule not found' });
    
    await schedule.markAttendance(studentId, status, recordedBy, notes);
    res.json({ message: 'Attendance marked successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
