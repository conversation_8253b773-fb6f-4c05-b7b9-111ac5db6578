const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

class EverSignService {
  constructor() {
    this.API_KEY = process.env.EVERSIGN_API_KEY;
    this.BUSINESS_ID = process.env.EVERSIGN_BUSINESS_ID;
    this.BASE_URL = 'https://api.eversign.com/api';
    this.WEBHOOK_URL = process.env.EVERSIGN_WEBHOOK_URL || 'http://localhost:5000/api/applications/eversign-webhook';

    console.log('🔧 Configuration EverSign:', {
      hasApiKey: !!this.API_KEY,
      businessId: this.BUSINESS_ID,
      baseUrl: this.BASE_URL
    });
  }

  // Configuration des headers d'authentification
  getAuthHeaders() {
    return {
      'Content-Type': 'application/json'
    };
  }

  // EverSign utilise l'API key comme paramètre, pas comme header
  getAuthParams() {
    return {
      access_key: this.API_KEY,
      business_id: this.BUSINESS_ID
    };
  }

  // Créer un document pour signature avec fichier
  async createDocumentWithFile(applicationData, documentPath) {
    try {
      console.log('📝 Création document EverSign avec fichier pour:', applicationData.email);

      // Vérifier la configuration
      if (!this.API_KEY || !this.BUSINESS_ID) {
        throw new Error('Configuration EverSign manquante (API_KEY ou BUSINESS_ID)');
      }

      // Préparer le fichier à uploader
      let fileToUpload = documentPath;
      let tempFileCreated = false;

      console.log('🔍 Vérification du fichier:', documentPath);
      console.log('📁 Fichier existe:', fs.existsSync(documentPath));

      if (!fs.existsSync(documentPath)) {
        // Créer un document temporaire avec les infos de candidature
        const tempContent = this.generateApplicationPDF(applicationData);
        fileToUpload = documentPath.replace('.pdf', '.txt');
        fs.writeFileSync(fileToUpload, tempContent);
        tempFileCreated = true;
        console.log('📄 Fichier temporaire créé:', fileToUpload);
      }

      // Vérifier que le fichier final existe
      if (!fs.existsSync(fileToUpload)) {
        throw new Error(`Fichier non trouvé: ${fileToUpload}`);
      }

      console.log('📊 Taille du fichier:', fs.statSync(fileToUpload).size, 'bytes');

      // Utiliser FormData pour envoyer le document avec le fichier
      const FormData = require('form-data');
      const form = new FormData();

      // Ajouter les métadonnées du document
      form.append('sandbox', process.env.NODE_ENV !== 'production' ? '1' : '0');
      form.append('title', `Candidature EMBA - ${applicationData.prenom} ${applicationData.nom}`);
      form.append('message', `Bonjour ${applicationData.prenom},\n\nVeuillez signer ce document pour finaliser votre candidature au programme EMBA.\n\nCordialement,\nÉquipe EMBA`);
      form.append('use_signer_order', '1');
      form.append('reminders', '1');
      form.append('require_all_signers', '1');
      form.append('redirect', `${process.env.BASE_URL || 'http://localhost:5000'}/api/applications/signature-complete`);
      form.append('redirect_decline', `${process.env.BASE_URL || 'http://localhost:5000'}/signature-declined`);
      form.append('client', applicationData.applicationId);

      // Ajouter le signataire
      form.append('signers[0][id]', '1');
      form.append('signers[0][name]', `${applicationData.prenom} ${applicationData.nom}`);
      form.append('signers[0][email]', applicationData.email);
      form.append('signers[0][message]', 'Veuillez signer votre candidature EMBA');

      // Ajouter les champs de signature
      form.append('fields[0][identifier]', 'signature_1');
      form.append('fields[0][x]', '400');
      form.append('fields[0][y]', '100');
      form.append('fields[0][width]', '200');
      form.append('fields[0][height]', '50');
      form.append('fields[0][page]', '1');
      form.append('fields[0][signer]', '1');
      form.append('fields[0][type]', 'signature');
      form.append('fields[0][required]', '1');

      form.append('fields[1][identifier]', 'date_1');
      form.append('fields[1][x]', '400');
      form.append('fields[1][y]', '160');
      form.append('fields[1][width]', '150');
      form.append('fields[1][height]', '30');
      form.append('fields[1][page]', '1');
      form.append('fields[1][signer]', '1');
      form.append('fields[1][type]', 'date_signed');
      form.append('fields[1][required]', '1');

      // Ajouter les métadonnées
      form.append('meta[application_id]', applicationData.applicationId);
      form.append('meta[application_number]', applicationData.applicationNumber || 'N/A');
      form.append('meta[candidate_email]', applicationData.email);

      // Ajouter le fichier avec gestion d'erreur
      try {
        const fileStream = fs.createReadStream(fileToUpload);
        form.append('file[0]', fileStream);
        console.log('📎 Fichier ajouté au FormData:', fileToUpload);
      } catch (streamError) {
        console.error('❌ Erreur création stream:', streamError);
        throw new Error(`Impossible de lire le fichier: ${fileToUpload}`);
      }

      console.log('📤 Envoi du document avec fichier vers EverSign...');

      // Envoyer la requête avec le fichier
      const response = await axios.post(`${this.BASE_URL}/document`, form, {
        headers: {
          ...form.getHeaders()
        },
        params: this.getAuthParams()
      });

      // Nettoyer le fichier temporaire si créé
      if (tempFileCreated && fs.existsSync(fileToUpload)) {
        fs.unlinkSync(fileToUpload);
        console.log('🗑️ Fichier temporaire supprimé');
      }

      console.log('🔍 Réponse EverSign complète:', JSON.stringify(response.data, null, 2));

      // EverSign peut retourner le hash dans différents champs selon l'API
      const documentHash = response.data.document_hash ||
                          response.data.hash ||
                          response.data.id ||
                          response.data.document?.hash ||
                          response.data.document?.document_hash;

      console.log('✅ Document EverSign créé:', documentHash);

      if (!documentHash) {
        throw new Error('Document hash non reçu d\'EverSign. Vérifiez la configuration API.');
      }

      // L'URL de signature peut être fournie directement par EverSign
      const signUrl = response.data.signing_url ||
                     response.data.sign_url ||
                     response.data.document?.signing_url ||
                     `https://eversign.com/sign/${documentHash}`;

      console.log('🔗 URL de signature générée:', signUrl);

      return {
        success: true,
        submissionId: documentHash, // Pour compatibilité avec les routes
        documentHash,
        signatureUrl: signUrl, // Pour compatibilité avec les routes
        signUrl,
        message: 'Document EverSign créé avec succès',
        provider: 'eversign'
      };

    } catch (error) {
      console.error('❌ Erreur EverSign:', error.response?.data || error.message);
      throw new Error(`Erreur EverSign: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Méthode principale pour créer un document
  async createDocument(applicationData, documentPath) {
    return this.createDocumentWithFile(applicationData, documentPath);
  }

  // Uploader un fichier vers EverSign
  async uploadFile(documentHash, filePath) {
    try {
      const form = new FormData();
      form.append('upload', fs.createReadStream(filePath));

      const response = await axios.post(`${this.BASE_URL}/document/upload_file`, form, {
        headers: {
          ...form.getHeaders()
        },
        params: {
          document_hash: documentHash,
          ...this.getAuthParams()
        }
      });

      console.log('✅ Fichier uploadé vers EverSign');
      return response.data;

    } catch (error) {
      console.error('❌ Erreur upload EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Obtenir le statut d'un document
  async getDocumentStatus(documentHash) {
    try {
      const response = await axios.get(`${this.BASE_URL}/document`, {
        headers: this.getAuthHeaders(),
        params: {
          document_hash: documentHash,
          ...this.getAuthParams()
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur statut EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Télécharger le document signé
  async downloadSignedDocument(documentHash) {
    try {
      const response = await axios.get(`${this.BASE_URL}/download_final_document`, {
        headers: this.getAuthHeaders(),
        params: {
          document_hash: documentHash,
          ...this.getAuthParams()
        },
        responseType: 'stream'
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur téléchargement EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Annuler un document
  async cancelDocument(documentHash) {
    try {
      const response = await axios.delete(`${this.BASE_URL}/document`, {
        headers: this.getAuthHeaders(),
        params: {
          document_hash: documentHash
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur annulation EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Processus complet de signature pour une candidature EMBA
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🔏 Début du processus EverSign pour:', applicationData.email);

      // Créer le document de signature
      const result = await this.createDocument(applicationData, documentPath);

      console.log('✅ Processus EverSign initié:', result.documentHash);

      return {
        success: true,
        submissionId: result.documentHash, // Utiliser documentHash comme submissionId
        signatureUrl: result.signUrl,
        message: 'Document EverSign créé avec succès',
        provider: 'eversign'
      };

    } catch (error) {
      console.error('❌ Erreur processus EverSign:', error);
      return {
        success: false,
        error: error.message,
        provider: 'eversign'
      };
    }
  }

  // Générer le contenu du document de candidature
  generateApplicationPDF(applicationData) {
    return `
CANDIDATURE PROGRAMME EMBA
==========================

Informations du candidat:
- Nom: ${applicationData.nom}
- Prénom: ${applicationData.prenom}
- Email: ${applicationData.email}
- Téléphone: ${applicationData.telephone}

Numéro de candidature: ${applicationData.applicationNumber || 'En cours d\'attribution'}
Date de soumission: ${new Date().toLocaleDateString('fr-FR')}

DÉCLARATION ET SIGNATURE
========================

Je, soussigné(e) ${applicationData.prenom} ${applicationData.nom}, déclare que:

1. Toutes les informations fournies dans cette candidature sont exactes et complètes
2. Je m'engage à respecter le règlement du programme EMBA
3. J'autorise le traitement de mes données personnelles dans le cadre de cette candidature
4. Je comprends que toute fausse déclaration peut entraîner le rejet de ma candidature

Cette signature électronique a la même valeur légale qu'une signature manuscrite.

Date: ${new Date().toLocaleDateString('fr-FR')}

Signature électronique: [Zone de signature EverSign]
`;
  }

  // Valider un webhook EverSign
  validateWebhook(signature, payload) {
    // EverSign utilise une signature HMAC pour valider les webhooks
    // Implémentation simplifiée - en production, utilisez la clé secrète
    return true;
  }
}

module.exports = new EverSignService();
