const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

class EverSignService {
  constructor() {
    this.API_KEY = process.env.EVERSIGN_API_KEY;
    this.BUSINESS_ID = process.env.EVERSIGN_BUSINESS_ID;
    this.BASE_URL = 'https://api.eversign.com/api';
    this.WEBHOOK_URL = process.env.EVERSIGN_WEBHOOK_URL || 'http://localhost:5000/api/applications/eversign-webhook';
  }

  // Configuration des headers d'authentification
  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.API_KEY}`,
      'Content-Type': 'application/json'
    };
  }

  // Créer un document pour signature
  async createDocument(applicationData, documentPath) {
    try {
      console.log('📝 Création document EverSign pour:', applicationData.email);

      // Vérifier la configuration
      if (!this.API_KEY || !this.BUSINESS_ID) {
        throw new Error('Configuration EverSign manquante (API_KEY ou BUSINESS_ID)');
      }

      // Préparer les données du document
      const documentData = {
        sandbox: process.env.NODE_ENV !== 'production' ? 1 : 0,
        title: `Candidature EMBA - ${applicationData.prenom} ${applicationData.nom}`,
        message: `Bonjour ${applicationData.prenom},\n\nVeuillez signer ce document pour finaliser votre candidature au programme EMBA.\n\nCordialement,\nÉquipe EMBA`,
        use_signer_order: 1,
        reminders: 1,
        require_all_signers: 1,
        redirect: `${process.env.BASE_URL || 'http://localhost:5000'}/signature-complete`,
        redirect_decline: `${process.env.BASE_URL || 'http://localhost:5000'}/signature-declined`,
        client: applicationData.applicationId,
        signers: [
          {
            id: 1,
            name: `${applicationData.prenom} ${applicationData.nom}`,
            email: applicationData.email,
            pin: '',
            message: 'Veuillez signer votre candidature EMBA'
          }
        ],
        recipients: [],
        fields: [
          {
            identifier: 'signature_1',
            x: 400,
            y: 100,
            width: 200,
            height: 50,
            page: 1,
            signer: 1,
            type: 'signature',
            required: 1
          },
          {
            identifier: 'date_1',
            x: 400,
            y: 160,
            width: 150,
            height: 30,
            page: 1,
            signer: 1,
            type: 'date_signed',
            required: 1
          }
        ],
        meta: {
          application_id: applicationData.applicationId,
          application_number: applicationData.applicationNumber || 'N/A',
          candidate_email: applicationData.email
        }
      };

      // Créer le document
      const response = await axios.post(`${this.BASE_URL}/document`, documentData, {
        headers: this.getAuthHeaders()
      });

      const documentHash = response.data.document_hash;
      console.log('✅ Document EverSign créé:', documentHash);

      // Uploader le fichier PDF si disponible
      if (fs.existsSync(documentPath)) {
        await this.uploadFile(documentHash, documentPath);
      } else {
        // Créer un document temporaire avec les infos de candidature
        const tempContent = this.generateApplicationPDF(applicationData);
        const tempPath = documentPath.replace('.pdf', '.txt');
        fs.writeFileSync(tempPath, tempContent);
        await this.uploadFile(documentHash, tempPath);
        fs.unlinkSync(tempPath); // Nettoyer le fichier temporaire
      }

      return {
        success: true,
        documentHash,
        signUrl: `https://eversign.com/sign/${documentHash}`,
        message: 'Document EverSign créé avec succès',
        provider: 'eversign'
      };

    } catch (error) {
      console.error('❌ Erreur EverSign:', error.response?.data || error.message);
      throw new Error(`Erreur EverSign: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Uploader un fichier vers EverSign
  async uploadFile(documentHash, filePath) {
    try {
      const form = new FormData();
      form.append('upload', fs.createReadStream(filePath));

      const response = await axios.post(`${this.BASE_URL}/document/upload_file`, form, {
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
          ...form.getHeaders()
        },
        params: {
          document_hash: documentHash
        }
      });

      console.log('✅ Fichier uploadé vers EverSign');
      return response.data;

    } catch (error) {
      console.error('❌ Erreur upload EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Obtenir le statut d'un document
  async getDocumentStatus(documentHash) {
    try {
      const response = await axios.get(`${this.BASE_URL}/document`, {
        headers: this.getAuthHeaders(),
        params: {
          document_hash: documentHash
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur statut EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Télécharger le document signé
  async downloadSignedDocument(documentHash) {
    try {
      const response = await axios.get(`${this.BASE_URL}/download_final_document`, {
        headers: this.getAuthHeaders(),
        params: {
          document_hash: documentHash
        },
        responseType: 'stream'
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur téléchargement EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Annuler un document
  async cancelDocument(documentHash) {
    try {
      const response = await axios.delete(`${this.BASE_URL}/document`, {
        headers: this.getAuthHeaders(),
        params: {
          document_hash: documentHash
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur annulation EverSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Processus complet de signature pour une candidature EMBA
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🔏 Début du processus EverSign pour:', applicationData.email);

      // Créer le document de signature
      const result = await this.createDocument(applicationData, documentPath);

      console.log('✅ Processus EverSign initié:', result.documentHash);

      return {
        success: true,
        submissionId: result.documentHash, // Utiliser documentHash comme submissionId
        signatureUrl: result.signUrl,
        message: 'Document EverSign créé avec succès',
        provider: 'eversign'
      };

    } catch (error) {
      console.error('❌ Erreur processus EverSign:', error);
      return {
        success: false,
        error: error.message,
        provider: 'eversign'
      };
    }
  }

  // Générer le contenu du document de candidature
  generateApplicationPDF(applicationData) {
    return `
CANDIDATURE PROGRAMME EMBA
==========================

Informations du candidat:
- Nom: ${applicationData.nom}
- Prénom: ${applicationData.prenom}
- Email: ${applicationData.email}
- Téléphone: ${applicationData.telephone}

Numéro de candidature: ${applicationData.applicationNumber || 'En cours d\'attribution'}
Date de soumission: ${new Date().toLocaleDateString('fr-FR')}

DÉCLARATION ET SIGNATURE
========================

Je, soussigné(e) ${applicationData.prenom} ${applicationData.nom}, déclare que:

1. Toutes les informations fournies dans cette candidature sont exactes et complètes
2. Je m'engage à respecter le règlement du programme EMBA
3. J'autorise le traitement de mes données personnelles dans le cadre de cette candidature
4. Je comprends que toute fausse déclaration peut entraîner le rejet de ma candidature

Cette signature électronique a la même valeur légale qu'une signature manuscrite.

Date: ${new Date().toLocaleDateString('fr-FR')}

Signature électronique: [Zone de signature EverSign]
`;
  }

  // Valider un webhook EverSign
  validateWebhook(signature, payload) {
    // EverSign utilise une signature HMAC pour valider les webhooks
    // Implémentation simplifiée - en production, utilisez la clé secrète
    return true;
  }
}

module.exports = new EverSignService();
