const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

class CanvasSignatureService {
  constructor() {
    this.BASE_URL = process.env.BASE_URL || 'http://localhost:5000';
    this.STORAGE_PATH = process.env.SIGNATURE_STORAGE_PATH || './uploads/signatures';
    
    // Créer le dossier de stockage s'il n'existe pas
    if (!fs.existsSync(this.STORAGE_PATH)) {
      fs.mkdirSync(this.STORAGE_PATH, { recursive: true });
    }
  }

  // Processus de signature pour une candidature
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🖊️ Début du processus de signature Canvas pour:', applicationData.email);
      
      // Générer des IDs uniques
      const submissionId = this.generateSignatureId('sig');
      const sessionToken = this.generateSessionToken(applicationData);
      
      console.log('✅ Session de signature créée:', { submissionId, sessionToken });
      
      return {
        success: true,
        submissionId,
        sessionToken,
        message: 'Session de signature électronique créée avec succès',
        signatureUrl: `${this.BASE_URL}/api/applications/signature/canvas/${submissionId}?token=${sessionToken}`,
        method: 'canvas',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24h
      };
      
    } catch (error) {
      console.error('❌ Erreur création session signature:', error);
      return {
        success: false,
        error: error.message,
        method: 'canvas'
      };
    }
  }

  // Générer un ID de signature unique
  generateSignatureId(prefix = 'sig') {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    return `${prefix}_${timestamp}_${random}`;
  }

  // Générer un token de session sécurisé
  generateSessionToken(applicationData) {
    const data = `${applicationData.email}-${applicationData.applicationId}-${Date.now()}`;
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 32);
  }

  // Valider un token de session
  validateSessionToken(token, applicationData) {
    // Pour simplifier, on accepte tous les tokens valides (32 caractères hex)
    return token && token.length === 32 && /^[a-f0-9]+$/.test(token);
  }

  // Sauvegarder une signature Canvas
  async saveCanvasSignature(submissionId, signatureData, applicationData) {
    try {
      const signatureInfo = {
        submissionId,
        applicationId: applicationData.applicationId,
        candidateEmail: applicationData.email,
        candidateName: `${applicationData.prenom} ${applicationData.nom}`,
        signatureData: signatureData.signature, // Base64 de l'image Canvas
        timestamp: new Date().toISOString(),
        ip: signatureData.ip || 'unknown',
        userAgent: signatureData.userAgent || 'unknown',
        method: 'canvas_html5'
      };

      // Sauvegarder les métadonnées
      const metadataPath = path.join(this.STORAGE_PATH, `${submissionId}_metadata.json`);
      fs.writeFileSync(metadataPath, JSON.stringify(signatureInfo, null, 2));

      // Sauvegarder l'image de signature (base64 vers fichier)
      if (signatureData.signature && signatureData.signature.startsWith('data:image/')) {
        const base64Data = signatureData.signature.replace(/^data:image\/png;base64,/, '');
        const imagePath = path.join(this.STORAGE_PATH, `${submissionId}_signature.png`);
        fs.writeFileSync(imagePath, base64Data, 'base64');
        signatureInfo.signatureImagePath = imagePath;
      }

      console.log('✅ Signature Canvas sauvegardée:', submissionId);
      return {
        success: true,
        submissionId,
        signatureInfo
      };

    } catch (error) {
      console.error('❌ Erreur sauvegarde signature:', error);
      throw error;
    }
  }

  // Obtenir les informations d'une signature
  async getSignatureInfo(submissionId) {
    try {
      const metadataPath = path.join(this.STORAGE_PATH, `${submissionId}_metadata.json`);
      
      if (!fs.existsSync(metadataPath)) {
        return null;
      }

      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      return metadata;

    } catch (error) {
      console.error('❌ Erreur lecture signature:', error);
      return null;
    }
  }

  // Vérifier si une signature existe
  async signatureExists(submissionId) {
    const metadataPath = path.join(this.STORAGE_PATH, `${submissionId}_metadata.json`);
    return fs.existsSync(metadataPath);
  }

  // Générer le HTML de la page de signature Canvas
  generateSignaturePage(submissionId, applicationData, sessionToken) {
    return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signature Électronique - EMBA</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px; 
            width: 100%;
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #dc2626, #b91c1c); 
            color: white; 
            padding: 30px; 
            text-align: center; 
        }
        .header h1 { font-size: 2em; margin-bottom: 10px; }
        .content { padding: 30px; }
        .info-card { 
            background: #f8fafc; 
            border: 1px solid #e2e8f0; 
            border-radius: 10px; 
            padding: 20px; 
            margin: 20px 0; 
        }
        .signature-area { 
            border: 3px dashed #dc2626; 
            border-radius: 10px; 
            padding: 20px; 
            text-align: center; 
            margin: 30px 0; 
            background: #fef2f2;
        }
        #signatureCanvas { 
            border: 2px solid #dc2626; 
            border-radius: 8px; 
            cursor: crosshair; 
            background: white;
            display: block;
            margin: 20px auto;
        }
        .btn { 
            background: #dc2626; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 16px; 
            margin: 10px; 
            transition: all 0.3s;
        }
        .btn:hover { background: #b91c1c; transform: translateY(-2px); }
        .btn:disabled { background: #9ca3af; cursor: not-allowed; transform: none; }
        .btn-secondary { background: #6b7280; }
        .btn-secondary:hover { background: #4b5563; }
        .alert { 
            padding: 15px; 
            border-radius: 8px; 
            margin: 20px 0; 
        }
        .alert-warning { background: #fef3c7; border: 1px solid #f59e0b; color: #92400e; }
        .alert-success { background: #d1fae5; border: 1px solid #10b981; color: #065f46; }
        .signature-info { font-size: 14px; color: #6b7280; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 Signature Électronique EMBA</h1>
            <p>Candidature N° ${applicationData.applicationNumber || 'En cours'}</p>
        </div>
        
        <div class="content">
            <div class="alert alert-warning">
                <strong>⚠️ Important:</strong> Cette signature électronique est obligatoire pour valider votre candidature au programme EMBA. 
                Elle a la même valeur légale qu'une signature manuscrite.
            </div>
            
            <div class="info-card">
                <h3>📋 Résumé de votre candidature</h3>
                <p><strong>Nom:</strong> ${applicationData.prenom} ${applicationData.nom}</p>
                <p><strong>Email:</strong> ${applicationData.email}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString('fr-FR')}</p>
            </div>
            
            <div class="signature-area">
                <h3>✍️ Votre signature électronique</h3>
                <p>Signez dans la zone ci-dessous avec votre souris ou votre doigt (sur mobile)</p>
                
                <canvas id="signatureCanvas" width="600" height="200"></canvas>
                
                <div>
                    <button class="btn btn-secondary" onclick="clearSignature()">🗑️ Effacer</button>
                    <button class="btn" onclick="saveSignature()" id="saveBtn" disabled>✅ Valider la signature</button>
                </div>
                
                <div class="signature-info">
                    💡 Conseil: Utilisez un trait continu pour une signature claire et lisible
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        const saveBtn = document.getElementById('saveBtn');
        
        let isDrawing = false;
        let hasSignature = false;
        
        // Configuration du canvas
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.lineCap = 'round';
        
        // Événements souris
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        
        // Événements tactiles (mobile)
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopDrawing);
        
        function startDrawing(e) {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            ctx.beginPath();
            ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
        }
        
        function draw(e) {
            if (!isDrawing) return;
            
            const rect = canvas.getBoundingClientRect();
            ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
            ctx.stroke();
            
            hasSignature = true;
            saveBtn.disabled = false;
        }
        
        function stopDrawing() {
            isDrawing = false;
        }
        
        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }
        
        function clearSignature() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            hasSignature = false;
            saveBtn.disabled = true;
        }
        
        async function saveSignature() {
            if (!hasSignature) {
                alert('Veuillez d\\'abord signer dans la zone prévue.');
                return;
            }
            
            if (!confirm('Êtes-vous sûr de vouloir valider cette signature électronique?')) {
                return;
            }
            
            try {
                saveBtn.disabled = true;
                saveBtn.textContent = '⏳ Enregistrement...';
                
                const signatureData = canvas.toDataURL('image/png');
                
                const response = await fetch('/api/applications/signature/canvas/save/${submissionId}', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        signature: signatureData,
                        sessionToken: '${sessionToken}',
                        timestamp: new Date().toISOString(),
                        userAgent: navigator.userAgent
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.querySelector('.content').innerHTML = \`
                        <div class="alert alert-success">
                            <h3>✅ Signature enregistrée avec succès!</h3>
                            <p>Votre candidature est maintenant complète et légalement valide.</p>
                            <p><strong>Numéro de candidature:</strong> \${result.applicationNumber}</p>
                        </div>
                        <div style="text-align: center; margin-top: 30px;">
                            <button class="btn" onclick="window.close()">Fermer cette fenêtre</button>
                        </div>
                    \`;
                } else {
                    throw new Error(result.message || 'Erreur lors de l\\'enregistrement');
                }
                
            } catch (error) {
                alert('❌ Erreur: ' + error.message);
                saveBtn.disabled = false;
                saveBtn.textContent = '✅ Valider la signature';
            }
        }
    </script>
</body>
</html>`;
  }
}

module.exports = new CanvasSignatureService();
