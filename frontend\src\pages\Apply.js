import React, { useState } from 'react';
import {
  UserIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import FloatingThemeToggle from '../components/FloatingThemeToggle';
import { TextInput, SelectInput, TextareaInput, FileInput, baseInputClasses, baseLabelClasses } from '../components/FormField';

// Fonction pour télécharger la lettre de recommandation
const downloadRecommendationLetter = () => {
  const link = document.createElement('a');
  link.href = 'http://localhost:5000/api/documents/download/recommendation-letter';
  link.download = 'Lettre_de_recommendation.pdf';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const Apply = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // INFORMATIONS GÉNÉRALES
    civilite: '', // 'Mme' ou 'Mr'
    nom: '',
    nomMarital: '',
    prenom: '',
    situationActuelle: '', // 'en_activite', 'recherche_emploi', 'creation_entreprise'
    employeurInforme: '', // 'oui', 'non'
    photo: null,
    dateNaissance: '',
    villeNaissance: '',
    paysNaissance: '',
    nationalite: '',
    telephone: '',
    telPortable: '',
    email: '',

    // PARCOURS ACADÉMIQUE
    parcoursAcademique: [
      { annee: '', diplome: '', etablissement: '', pays: '' }
    ],

    // EXPÉRIENCE PROFESSIONNELLE
    nombreAnneesExperience: '',
    anneesResponsabilite: '',

    // EMPLOI ACTUEL OU DERNIER POSTE OCCUPÉ
    fonctionOccupee: '',
    dureeDebut: '', // mois/année
    dureeFin: '', // mois/année
    nombreSubordonnes: '',
    tempsPlein: true, // true pour temps plein, false pour temps partiel
    nomEntreprise: '',
    secteurActivite: '',
    adresseEntreprise: '',
    telephoneEntreprise: '',
    descriptionPoste: '',

    // EXPÉRIENCE PROFESSIONNELLE SIGNIFICATIVE ANTÉRIEURE 1
    experienceAnterieure1: {
      fonction: '',
      dureeDebut: '', // mois/année
      dureeFin: '', // mois/année
      nomEntreprise: '',
      description: ''
    },

    // EXPÉRIENCE PROFESSIONNELLE SIGNIFICATIVE ANTÉRIEURE 2
    experienceAnterieure2: {
      fonction: '',
      dureeDebut: '', // mois/année
      dureeFin: '', // mois/année
      nomEntreprise: '',
      description: ''
    },

    // Situation professionnelle satisfaisante
    situationSatisfaisante: '',

    // LANGUES
    langues: {
      francais: {
        parle: '', // 'courant', 'bon', 'moyen', 'debutant'
        lu: '',
        ecrit: ''
      },
      anglais: {
        parle: '',
        lu: '',
        ecrit: ''
      },
      autre: {
        nom: '',
        parle: '',
        lu: '',
        ecrit: ''
      }
    },
    certificationLangues: '', // Tests reconnus (TOEFL, IELTS, etc.)
    languesEtrangeresPro: '', // 'oui', 'non'
    contexteLangues: '',

    // AUTRES ACTIVITÉS
    vecuEtranger: '', // 'oui', 'non'
    detailsEtranger: '', // pays, circonstances, durée
    passionCentreInteret: '',

    // PROJET PROFESSIONNEL
    projetProfessionnel: '',
    attentesFormation: '',

    // CANDIDATURE
    candidatAutresProgrammes: '', // 'oui', 'non'
    autresProgrammes: '',
    financement: '', // 'personnel', 'employeur_total', 'employeur_partiel', 'autre'
    financementAutre: '',
    commentConnu: '', // 'site_internet', 'publicite', 'presse', 'entreprise', 'ami', 'recherche_internet', 'session_info', 'ancien_eleve', 'autre'
    commentConnuAutre: '',

    // PIÈCES À JOINDRE AU DOSSIER
    cv: null, // Un curriculum vitae détaillé
    cin: null, // Photocopie de votre pièce d'identité
    photo: null, // Une photo d'identité récente
    diplomes: null, // Copies certifiées conformes diplômes
    lettresRecommandation: null, // Une ou deux lettres de recommandation
    attestationExperience: null // Facultatif: Attestation d'expérience professionnelle
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    { id: 1, name: 'Informations Générales', icon: UserIcon },
    { id: 2, name: 'Parcours Académique', icon: AcademicCapIcon },
    { id: 3, name: 'Expérience Professionnelle', icon: BriefcaseIcon },
    { id: 4, name: 'Langues', icon: DocumentTextIcon },
    { id: 5, name: 'Autres Activités & Projet', icon: DocumentTextIcon },
    { id: 6, name: 'Candidature & Documents', icon: CloudArrowUpIcon }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files[0]) {
      setFormData(prev => ({
        ...prev,
        [name]: files[0]
      }));
    }
  };

  // Gestion des tableaux dynamiques
  const handleArrayChange = (arrayName, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: prev[arrayName].map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const addArrayItem = (arrayName, defaultItem) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: [...prev[arrayName], defaultItem]
    }));
  };

  const removeArrayItem = (arrayName, index) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: prev[arrayName].filter((_, i) => i !== index)
    }));
  };

  // Gestion des objets imbriqués (langues, expériences antérieures)
  const handleNestedChange = (objectName, field, value) => {
    setFormData(prev => ({
      ...prev,
      [objectName]: {
        ...prev[objectName],
        [field]: value
      }
    }));
  };

  const handleLangueChange = (langue, competence, value) => {
    setFormData(prev => ({
      ...prev,
      langues: {
        ...prev.langues,
        [langue]: {
          ...prev.langues[langue],
          [competence]: value
        }
      }
    }));
  };

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1:
        if (!formData.firstName) newErrors.firstName = 'Prénom requis';
        if (!formData.lastName) newErrors.lastName = 'Nom requis';
        if (!formData.email) newErrors.email = 'Email requis';
        if (!formData.phone) newErrors.phone = 'Téléphone requis';
        if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date de naissance requise';
        if (!formData.gender) newErrors.gender = 'Genre requis';
        break;
      case 2:
        if (!formData.lastDegree) newErrors.lastDegree = 'Dernier diplôme requis';
        if (!formData.institution) newErrors.institution = 'Institution requise';
        if (!formData.graduationYear) newErrors.graduationYear = 'Année de diplôme requise';
        break;
      case 3:
        if (!formData.currentPosition) newErrors.currentPosition = 'Poste actuel requis';
        if (!formData.currentCompany) newErrors.currentCompany = 'Entreprise actuelle requise';
        if (!formData.yearsOfExperience) newErrors.yearsOfExperience = 'Années d\'expérience requises';
        break;
      case 4:
        if (!formData.whyEMBA) newErrors.whyEMBA = 'Motivation pour l\'EMBA requise';
        if (!formData.careerGoals) newErrors.careerGoals = 'Objectifs de carrière requis';
        if (!formData.motivation) newErrors.motivation = 'Attentes du programme requises';
        if (!formData.fundingSource) newErrors.fundingSource = 'Source de financement requise';
        break;
      case 5:
        if (!formData.cv) newErrors.cv = 'CV requis';
        if (!formData.motivationLetter) newErrors.motivationLetter = 'Lettre de motivation requise';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 5));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(5)) return;

    setIsSubmitting(true);

    try {
      const formDataToSend = new FormData();

      // Add all form fields (except files)
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '' && !(formData[key] instanceof File)) {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add files separately
      if (formData.cv) formDataToSend.append('cv', formData.cv);
      if (formData.motivationLetter) formDataToSend.append('motivationLetter', formData.motivationLetter);
      if (formData.diploma) formDataToSend.append('diploma', formData.diploma);
      if (formData.transcripts) formDataToSend.append('transcripts', formData.transcripts);
      if (formData.photo) formDataToSend.append('photo', formData.photo);

      console.log('📤 Envoi de la candidature...');

      const response = await fetch('http://localhost:5000/api/applications/submit', {
        method: 'POST',
        body: formDataToSend
      });

      const result = await response.json();

      if (result.success) {
        alert(`Candidature soumise avec succès !\nNuméro de candidature: ${result.applicationNumber}`);
        // Reset form or redirect
        setCurrentStep(1);
        setFormData({
          firstName: '', lastName: '', email: '', phone: '', dateOfBirth: '', gender: '',
          nationality: '', address: '', city: '', postalCode: '', country: '',
          lastDegree: '', institution: '', graduationYear: '', gpa: '',
          currentPosition: '', currentCompany: '', yearsOfExperience: '',
          managementExperience: '', industry: '', teamSize: '',
          motivation: '', careerGoals: '', whyEMBA: '', specialization: '', fundingSource: '',
          cv: null, diploma: null, transcripts: null, motivationLetter: null, photo: null
        });
      } else {
        throw new Error(result.message || 'Erreur lors de la soumission');
      }

    } catch (error) {
      console.error('❌ Erreur:', error);
      alert(`Erreur lors de la soumission: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">INFORMATIONS GÉNÉRALES</h3>

            {/* Civilité */}
            <div>
              <label className={baseLabelClasses}>Civilité *</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="civilite"
                    value="Mme"
                    checked={formData.civilite === 'Mme'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  Mme
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="civilite"
                    value="Mr"
                    checked={formData.civilite === 'Mr'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  Mr
                </label>
              </div>
            </div>

            {/* Nom, Nom Marital, Prénom */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <TextInput
                label="Nom *"
                name="nom"
                value={formData.nom}
                onChange={handleInputChange}
                error={errors.nom}
                required
              />
              <TextInput
                label="Nom Marital"
                name="nomMarital"
                value={formData.nomMarital}
                onChange={handleInputChange}
              />
              <TextInput
                label="Prénom *"
                name="prenom"
                value={formData.prenom}
                onChange={handleInputChange}
                error={errors.prenom}
                required
              />
            </div>

            {/* Situation Actuelle */}
            <div>
              <label className={baseLabelClasses}>SITUATION ACTUELLE *</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="situationActuelle"
                    value="en_activite"
                    checked={formData.situationActuelle === 'en_activite'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  En activité
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="situationActuelle"
                    value="recherche_emploi"
                    checked={formData.situationActuelle === 'recherche_emploi'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  A la recherche d'emploi
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="situationActuelle"
                    value="creation_entreprise"
                    checked={formData.situationActuelle === 'creation_entreprise'}
                    onChange={handleInputChange}
                    className="mr-2 text-red-600 focus:ring-red-500"
                  />
                  En création/ reprise d'entreprise
                </label>
              </div>
            </div>

            {/* Question employeur informé */}
            {formData.situationActuelle === 'en_activite' && (
              <div>
                <label className={baseLabelClasses}>
                  Si vous êtes en activité, votre employeur est-il informé de votre candidature à l'Exécutive MBA ?
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="employeurInforme"
                      value="oui"
                      checked={formData.employeurInforme === 'oui'}
                      onChange={handleInputChange}
                      className="mr-2 text-red-600 focus:ring-red-500"
                    />
                    Oui
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="employeurInforme"
                      value="non"
                      checked={formData.employeurInforme === 'non'}
                      onChange={handleInputChange}
                      className="mr-2 text-red-600 focus:ring-red-500"
                    />
                    Non
                  </label>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  ⚠️ Toute modification intervenant au cours de l'année devra être obligatoirement signalée à la direction de l'EMBA.
                </p>
              </div>
            )}

            {/* Photo */}
            <div>
              <FileInput
                label="Photo *"
                name="photo"
                onChange={handleFileChange}
                accept="image/*"
                error={errors.photo}
                required
              />
            </div>

            {/* Informations personnelles */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Prénom *
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Votre prénom"
                />
                {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Nom *
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Votre nom"
                />
                {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Email *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Téléphone *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="+216 XX XXX XXX"
                />
                {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Date de naissance *
                </label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                />
                {errors.dateOfBirth && <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Genre *
                </label>
                <SelectInput
                  name="gender"
                  value={formData.gender}
                  onChange={handleInputChange}
                  options={[
                    { value: '', label: 'Sélectionnez votre genre' },
                    { value: 'Male', label: 'Homme' },
                    { value: 'Female', label: 'Femme' },
                    { value: 'Other', label: 'Autre' }
                  ]}
                />
                {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Nationalité
                </label>
                <input
                  type="text"
                  name="nationality"
                  value={formData.nationality}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Votre nationalité"
                />
              </div>
            </div>

            <div>
              <label className={baseLabelClasses}>
                Adresse
              </label>
              <input
                type="text"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className={baseInputClasses}
                placeholder="Votre adresse complète"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Ville
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Ville"
                />
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Code postal
                </label>
                <input
                  type="text"
                  name="postalCode"
                  value={formData.postalCode}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Code postal"
                />
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Pays
                </label>
                <select
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                >
                  <option value="">Sélectionnez un pays</option>
                  <option value="TN">Tunisie</option>
                  <option value="FR">France</option>
                  <option value="MA">Maroc</option>
                  <option value="DZ">Algérie</option>
                  <option value="other">Autre</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Formation académique</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Dernier diplôme obtenu *
                </label>
                <SelectInput
                  name="lastDegree"
                  value={formData.lastDegree}
                  onChange={handleInputChange}
                  options={[
                    { value: '', label: 'Sélectionnez votre diplôme' },
                    { value: 'High School', label: 'Baccalauréat' },
                    { value: 'Bachelor', label: 'Licence / Bachelor' },
                    { value: 'Master', label: 'Master / Maîtrise' },
                    { value: 'PhD', label: 'Doctorat / PhD' },
                    { value: 'Professional Certificate', label: 'Certificat professionnel' },
                    { value: 'Other', label: 'Autre' }
                  ]}
                />
                {errors.lastDegree && <p className="text-red-500 text-sm mt-1">{errors.lastDegree}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Institution *
                </label>
                <input
                  type="text"
                  name="institution"
                  value={formData.institution}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Nom de l'université/école"
                />
                {errors.institution && <p className="text-red-500 text-sm mt-1">{errors.institution}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Année d'obtention *
                </label>
                <select
                  name="graduationYear"
                  value={formData.graduationYear}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                >
                  <option value="">Sélectionnez l'année</option>
                  {Array.from({ length: 30 }, (_, i) => {
                    const year = new Date().getFullYear() - i;
                    return <option key={year} value={year}>{year}</option>;
                  })}
                </select>
                {errors.graduationYear && <p className="text-red-500 text-sm mt-1">{errors.graduationYear}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  GPA (optionnel, sur 4.0)
                </label>
                <input
                  type="number"
                  name="gpa"
                  value={formData.gpa}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Ex: 3.8"
                  min="0"
                  max="4"
                  step="0.1"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Expérience professionnelle</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Poste actuel *
                </label>
                <input
                  type="text"
                  name="currentPosition"
                  value={formData.currentPosition}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Votre titre de poste"
                />
                {errors.currentPosition && <p className="text-red-500 text-sm mt-1">{errors.currentPosition}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Entreprise actuelle *
                </label>
                <input
                  type="text"
                  name="currentCompany"
                  value={formData.currentCompany}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                  placeholder="Nom de votre entreprise"
                />
                {errors.currentCompany && <p className="text-red-500 text-sm mt-1">{errors.currentCompany}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  Années d'expérience *
                </label>
                <select
                  name="yearsOfExperience"
                  value={formData.yearsOfExperience}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                >
                  <option value="">Sélectionnez</option>
                  <option value="3-5">3-5 ans</option>
                  <option value="5-8">5-8 ans</option>
                  <option value="8-12">8-12 ans</option>
                  <option value="12-15">12-15 ans</option>
                  <option value="15+">15+ ans</option>
                </select>
                {errors.yearsOfExperience && <p className="text-red-500 text-sm mt-1">{errors.yearsOfExperience}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Expérience management
                </label>
                <select
                  name="managementExperience"
                  value={formData.managementExperience}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                >
                  <option value="">Sélectionnez</option>
                  <option value="0-2">0-2 ans</option>
                  <option value="2-5">2-5 ans</option>
                  <option value="5-10">5-10 ans</option>
                  <option value="10+">10+ ans</option>
                </select>
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Taille de l'équipe
                </label>
                <select
                  name="teamSize"
                  value={formData.teamSize}
                  onChange={handleInputChange}
                  className={baseInputClasses}
                >
                  <option value="">Sélectionnez</option>
                  <option value="1-5">1-5 personnes</option>
                  <option value="5-15">5-15 personnes</option>
                  <option value="15-50">15-50 personnes</option>
                  <option value="50+">50+ personnes</option>
                </select>
              </div>
            </div>

            <div>
              <label className={baseLabelClasses}>
                Secteur d'activité
              </label>
              <select
                name="industry"
                value={formData.industry}
                onChange={handleInputChange}
                className={baseInputClasses}
              >
                <option value="">Sélectionnez votre secteur</option>
                <option value="finance">Finance & Banque</option>
                <option value="tech">Technologie</option>
                <option value="consulting">Conseil</option>
                <option value="manufacturing">Industrie</option>
                <option value="healthcare">Santé</option>
                <option value="education">Éducation</option>
                <option value="retail">Commerce</option>
                <option value="telecom">Télécommunications</option>
                <option value="energy">Énergie</option>
                <option value="other">Autre</option>
              </select>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Motivation et projet professionnel</h3>

            <div>
              <label className={baseLabelClasses}>
                Spécialisation souhaitée
              </label>
              <SelectInput
                name="specialization"
                value={formData.specialization}
                onChange={handleInputChange}
                options={[
                  { value: '', label: 'Sélectionnez une spécialisation' },
                  { value: 'General Management', label: 'Management Général' },
                  { value: 'Finance', label: 'Finance & Stratégie' },
                  { value: 'Marketing', label: 'Marketing & Digital' },
                  { value: 'Operations', label: 'Operations & Supply Chain' },
                  { value: 'Entrepreneurship', label: 'Entrepreneuriat' },
                  { value: 'Digital Transformation', label: 'Transformation Digitale' },
                  { value: 'Strategy', label: 'Stratégie' }
                ]}
              />
            </div>

            <div>
              <label className={baseLabelClasses}>
                Pourquoi souhaitez-vous intégrer ce programme EMBA ? *
              </label>
              <TextareaInput
                name="whyEMBA"
                value={formData.whyEMBA}
                onChange={handleInputChange}
                rows={4}
                placeholder="Expliquez vos motivations pour rejoindre le programme EMBA..."
              />
              {errors.whyEMBA && <p className="text-red-500 text-sm mt-1">{errors.whyEMBA}</p>}
            </div>

            <div>
              <label className={baseLabelClasses}>
                Quels sont vos objectifs de carrière à court et long terme ? *
              </label>
              <TextareaInput
                name="careerGoals"
                value={formData.careerGoals}
                onChange={handleInputChange}
                rows={4}
                placeholder="Décrivez vos objectifs professionnels..."
              />
              {errors.careerGoals && <p className="text-red-500 text-sm mt-1">{errors.careerGoals}</p>}
            </div>

            <div>
              <label className={baseLabelClasses}>
                Qu'attendez-vous de ce programme pour votre développement professionnel ? *
              </label>
              <TextareaInput
                name="motivation"
                value={formData.motivation}
                onChange={handleInputChange}
                rows={4}
                placeholder="Expliquez comment ce programme vous aidera dans votre développement..."
              />
              {errors.motivation && <p className="text-red-500 text-sm mt-1">{errors.motivation}</p>}
            </div>

            <div>
              <label className={baseLabelClasses}>
                Source de financement *
              </label>
              <SelectInput
                name="fundingSource"
                value={formData.fundingSource}
                onChange={handleInputChange}
                options={[
                  { value: '', label: 'Sélectionnez votre source de financement' },
                  { value: 'self_funded', label: 'Autofinancement' },
                  { value: 'employer_sponsored', label: 'Pris en charge par l\'employeur' },
                  { value: 'loan', label: 'Prêt bancaire' },
                  { value: 'scholarship', label: 'Bourse d\'études' },
                  { value: 'mixed', label: 'Financement mixte' },
                  { value: 'other', label: 'Autre' }
                ]}
              />
              {errors.fundingSource && <p className="text-red-500 text-sm mt-1">{errors.fundingSource}</p>}
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Documents requis</h3>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Formats acceptés :</p>
                  <p>PDF, DOC, DOCX (max 5MB par fichier)</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={baseLabelClasses}>
                  CV actualisé *
                </label>
                <input
                  type="file"
                  name="cv"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  className={baseInputClasses}
                />
                {errors.cv && <p className="text-red-500 text-sm mt-1">{errors.cv}</p>}
                {formData.cv && <p className="text-green-600 text-sm mt-1">✓ {formData.cv.name}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Lettre de motivation *
                </label>
                <input
                  type="file"
                  name="motivationLetter"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  className={baseInputClasses}
                />
                {errors.motivationLetter && <p className="text-red-500 text-sm mt-1">{errors.motivationLetter}</p>}
                {formData.motivationLetter && <p className="text-green-600 text-sm mt-1">✓ {formData.motivationLetter.name}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Diplôme (optionnel)
                </label>
                <input
                  type="file"
                  name="diploma"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  className={baseInputClasses}
                />
                {formData.diploma && <p className="text-green-600 text-sm mt-1">✓ {formData.diploma.name}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Relevés de notes (optionnel)
                </label>
                <input
                  type="file"
                  name="transcripts"
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx"
                  className={baseInputClasses}
                />
                {formData.transcripts && <p className="text-green-600 text-sm mt-1">✓ {formData.transcripts.name}</p>}
              </div>

              <div>
                <label className={baseLabelClasses}>
                  Photo d'identité (optionnel)
                </label>
                <input
                  type="file"
                  name="photo"
                  onChange={handleFileChange}
                  accept=".jpg,.jpeg,.png"
                  className={baseInputClasses}
                />
                {formData.photo && <p className="text-green-600 text-sm mt-1">✓ {formData.photo.name}</p>}
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6 transition-colors duration-200">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Récapitulatif de votre candidature</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
                <div>
                  <p><span className="font-medium">Nom :</span> {formData.firstName} {formData.lastName}</p>
                  <p><span className="font-medium">Email :</span> {formData.email}</p>
                  <p><span className="font-medium">Téléphone :</span> {formData.phone}</p>
                </div>
                <div>
                  <p><span className="font-medium">Poste actuel :</span> {formData.currentPosition}</p>
                  <p><span className="font-medium">Entreprise :</span> {formData.currentCompany}</p>
                  <p><span className="font-medium">Expérience :</span> {formData.yearsOfExperience}</p>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">Avant de soumettre :</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Vérifiez que tous les champs obligatoires sont remplis</li>
                    <li>Assurez-vous que vos documents sont au bon format</li>
                    <li>Relisez vos réponses aux questions de motivation</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Étape {currentStep} en cours de développement...</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm transition-colors duration-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <div className="flex-1 text-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Candidature EMBA</h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mt-2">
                Rejoignez la prochaine cohorte d'Executive MBA
              </p>
            </div>

            {/* Bouton de téléchargement lettre de recommandation */}
            <div className="flex-shrink-0">
              <button
                onClick={downloadRecommendationLetter}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Lettre de Recommandation
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;

            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
                  isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : isActive
                      ? 'bg-red-600 border-red-600 text-white'
                      : 'bg-white border-gray-300 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircleIcon className="h-6 w-6" />
                  ) : (
                    <Icon className="h-6 w-6" />
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    isActive ? 'text-red-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`hidden sm:block w-16 h-0.5 ml-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            );
          })}
        </div>

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 transition-colors duration-200">
          {renderStepContent()}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Précédent
            </button>

            {currentStep < 5 ? (
              <button
                onClick={nextStep}
                className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Suivant
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Soumission...' : 'Soumettre la candidature'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Floating Theme Toggle */}
      <FloatingThemeToggle />
    </div>
  );
};

export default Apply;
