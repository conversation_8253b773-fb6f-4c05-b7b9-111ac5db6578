const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Student = require('../models/Student');
const Professor = require('../models/Professor');
const Application = require('../models/Application');
const Course = require('../models/Course');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/admin/dashboard/stats - Statistiques du dashboard admin
router.get('/dashboard/stats', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('📊 Récupération des statistiques du dashboard admin');

    // Statistiques des étudiants
    const totalStudents = await Student.countDocuments();
    const activeStudents = await Student.countDocuments({ status: 'active' });
    const studentsThisMonth = await Student.countDocuments({
      createdAt: { $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) }
    });

    // Statistiques des professeurs
    const totalProfessors = await Professor.countDocuments();
    const activeProfessors = await Professor.countDocuments({ employmentStatus: 'active' });
    const professorsThisMonth = await Professor.countDocuments({
      createdAt: { $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) }
    });

    // Statistiques des candidatures
    const totalApplications = await Application.countDocuments();
    const pendingApplications = await Application.countDocuments({ status: 'pending' });
    const approvedApplications = await Application.countDocuments({ status: 'approved' });
    const rejectedApplications = await Application.countDocuments({ status: 'rejected' });

    // Statistiques des cours
    const totalCourses = await Course.countDocuments();
    const activeCourses = await Course.countDocuments({ status: 'active' });
    const coursesThisMonth = await Course.countDocuments({
      createdAt: { $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1) }
    });

    // Calcul des tendances (comparaison avec le mois précédent)
    const lastMonth = new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1);
    const thisMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);

    const studentsLastMonth = await Student.countDocuments({
      createdAt: { $gte: lastMonth, $lt: thisMonth }
    });
    const professorsLastMonth = await Professor.countDocuments({
      createdAt: { $gte: lastMonth, $lt: thisMonth }
    });
    const coursesLastMonth = await Course.countDocuments({
      createdAt: { $gte: lastMonth, $lt: thisMonth }
    });

    const studentsTrend = studentsThisMonth - studentsLastMonth;
    const professorsTrend = professorsThisMonth - professorsLastMonth;
    const coursesTrend = coursesThisMonth - coursesLastMonth;

    const stats = {
      students: {
        total: totalStudents,
        active: activeStudents,
        newThisMonth: studentsThisMonth,
        trend: studentsTrend,
        change: studentsThisMonth > 0 ? `+${studentsThisMonth} ce mois` : 'Aucun nouveau ce mois'
      },
      professors: {
        total: totalProfessors,
        active: activeProfessors,
        newThisMonth: professorsThisMonth,
        trend: professorsTrend,
        change: professorsThisMonth > 0 ? `+${professorsThisMonth} ce mois` : 'Stable'
      },
      applications: {
        total: totalApplications,
        pending: pendingApplications,
        approved: approvedApplications,
        rejected: rejectedApplications,
        trend: 0,
        change: `${pendingApplications} en attente`
      },
      courses: {
        total: totalCourses,
        active: activeCourses,
        newThisMonth: coursesThisMonth,
        trend: coursesTrend,
        change: coursesThisMonth > 0 ? `+${coursesThisMonth} ce mois` : 'Aucun nouveau ce mois'
      },
      revenue: {
        total: 0, // À implémenter avec le système de paiement
        trend: 0,
        change: 'Données non disponibles'
      }
    };

    console.log('✅ Statistiques récupérées:', stats);

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/admin/dashboard/activities - Activités récentes
router.get('/dashboard/activities', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('📋 Récupération des activités récentes');

    const activities = [];

    // Récupérer les dernières candidatures
    const recentApplications = await Application.find()
      .sort({ createdAt: -1 })
      .limit(3);

    recentApplications.forEach(app => {
      activities.push({
        id: `app_${app._id}`,
        type: 'application',
        message: `Nouvelle candidature de ${app.firstName} ${app.lastName}`,
        time: getTimeAgo(app.createdAt),
        icon: 'DocumentTextIcon',
        color: 'primary',
        createdAt: app.createdAt
      });
    });

    // Récupérer les nouveaux étudiants
    try {
      const recentStudents = await Student.find()
        .sort({ createdAt: -1 })
        .limit(3)
        .populate('user', 'firstName lastName email');

      recentStudents.forEach(student => {
        if (student.user) {
          activities.push({
            id: `student_${student._id}`,
            type: 'enrollment',
            message: `Nouvel étudiant inscrit: ${student.user.firstName} ${student.user.lastName}`,
            time: getTimeAgo(student.createdAt),
            icon: 'AcademicCapIcon',
            color: 'success',
            createdAt: student.createdAt
          });
        }
      });
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des étudiants:', error);
    }

    // Récupérer les nouveaux professeurs
    try {
      const recentProfessors = await Professor.find()
        .sort({ createdAt: -1 })
        .limit(2)
        .populate('user', 'firstName lastName email');

      recentProfessors.forEach(prof => {
        if (prof.user) {
          activities.push({
            id: `prof_${prof._id}`,
            type: 'professor',
            message: `Nouveau professeur ajouté: ${prof.user.firstName} ${prof.user.lastName}`,
            time: getTimeAgo(prof.createdAt),
            icon: 'UserGroupIcon',
            color: 'info',
            createdAt: prof.createdAt
          });
        }
      });
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des professeurs:', error);
    }

    // Trier par date décroissante et limiter à 10
    activities.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    const limitedActivities = activities.slice(0, 10);

    console.log(`✅ ${limitedActivities.length} activités récupérées`);

    res.json({
      success: true,
      activities: limitedActivities
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des activités:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des activités',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/admin/dashboard/events - Événements à venir
router.get('/dashboard/events', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('📅 Récupération des événements à venir');

    // Récupérer le nombre total d'étudiants
    const totalStudents = await Student.countDocuments();

    // Pour l'instant, retourner des événements par défaut
    // À implémenter avec un vrai système de calendrier
    const events = [
      {
        id: 1,
        title: 'Réunion équipe pédagogique',
        time: '10:00 - 11:30',
        date: 'Aujourd\'hui',
        instructor: 'Administration',
        students: null,
        type: 'meeting'
      },
      {
        id: 2,
        title: 'Session d\'orientation nouveaux étudiants',
        time: '14:00 - 16:00',
        date: 'Demain',
        instructor: 'Équipe administrative',
        students: totalStudents || 0,
        type: 'orientation'
      }
    ];

    console.log(`✅ ${events.length} événements récupérés`);

    res.json({
      success: true,
      events
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des événements:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des événements',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Fonction utilitaire pour calculer le temps écoulé
function getTimeAgo(date) {
  const now = new Date();
  const diffInMs = now - new Date(date);
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 60) {
    return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
  } else if (diffInHours < 24) {
    return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
  } else {
    return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
  }
}

module.exports = router;
