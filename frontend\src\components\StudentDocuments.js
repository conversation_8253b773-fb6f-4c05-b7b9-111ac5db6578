import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  CloudArrowUpIcon,
  EyeIcon,
  TrashIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  FolderIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';

const StudentDocuments = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Données exemple - à remplacer par de vraies données de l'API
  const [documentsData] = useState([
    {
      id: 1,
      name: 'CV_Achraf_2024.pdf',
      type: 'cv_resume',
      category: 'Application',
      size: '245 KB',
      uploadDate: '2024-09-15',
      status: 'verified',
      description: 'CV mis à jour pour candidature EMBA',
      url: '/documents/cv_achraf_2024.pdf'
    },
    {
      id: 2,
      name: 'Diplome_Master.pdf',
      type: 'diploma',
      category: 'Academic',
      size: '1.2 MB',
      uploadDate: '2024-09-15',
      status: 'verified',
      description: 'Diplôme de Master en Informatique',
      url: '/documents/diplome_master.pdf'
    },
    {
      id: 3,
      name: 'Releves_Notes_Master.pdf',
      type: 'transcript',
      category: 'Academic',
      size: '890 KB',
      uploadDate: '2024-09-15',
      status: 'verified',
      description: 'Relevés de notes du Master',
      url: '/documents/releves_notes.pdf'
    },
    {
      id: 4,
      name: 'Lettre_Motivation_EMBA.pdf',
      type: 'personal_statement',
      category: 'Application',
      size: '156 KB',
      uploadDate: '2024-09-16',
      status: 'pending',
      description: 'Lettre de motivation pour le programme EMBA',
      url: '/documents/lettre_motivation.pdf'
    },
    {
      id: 5,
      name: 'Photo_Identite.jpg',
      type: 'photo',
      category: 'Identity',
      size: '89 KB',
      uploadDate: '2024-09-16',
      status: 'verified',
      description: 'Photo d\'identité officielle',
      url: '/documents/photo_identite.jpg'
    },
    {
      id: 6,
      name: 'Certificat_Travail_2024.pdf',
      type: 'work_certificate',
      category: 'Professional',
      size: '234 KB',
      uploadDate: '2024-10-01',
      status: 'pending',
      description: 'Certificat de travail actuel',
      url: '/documents/certificat_travail.pdf'
    }
  ]);

  const documentTypes = {
    'cv_resume': 'CV',
    'diploma': 'Diplôme',
    'transcript': 'Relevé de notes',
    'personal_statement': 'Lettre de motivation',
    'photo': 'Photo',
    'work_certificate': 'Certificat de travail',
    'recommendation_letter': 'Lettre de recommandation',
    'other': 'Autre'
  };

  const categories = [
    { value: 'all', label: 'Tous les documents' },
    { value: 'Application', label: 'Candidature' },
    { value: 'Academic', label: 'Académique' },
    { value: 'Professional', label: 'Professionnel' },
    { value: 'Identity', label: 'Identité' }
  ];

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      // TODO: Remplacer par un vrai appel API
      // const response = await fetch('/api/students/me/documents', {
      //   headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      // });
      // const data = await response.json();
      // setDocuments(data.documents);
      
      // Simulation d'un délai de chargement
      setTimeout(() => {
        setDocuments(documentsData);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors du chargement des documents:', error);
      setLoading(false);
    }
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    setUploading(true);
    try {
      // TODO: Implémenter l'upload réel
      // const formData = new FormData();
      // files.forEach(file => formData.append('documents', file));
      // const response = await fetch('/api/students/me/documents', {
      //   method: 'POST',
      //   headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
      //   body: formData
      // });
      
      // Simulation d'upload
      setTimeout(() => {
        const newDocuments = files.map((file, index) => ({
          id: documents.length + index + 1,
          name: file.name,
          type: 'other',
          category: 'Other',
          size: `${(file.size / 1024).toFixed(0)} KB`,
          uploadDate: new Date().toISOString().split('T')[0],
          status: 'pending',
          description: 'Document uploadé récemment',
          url: URL.createObjectURL(file)
        }));
        
        setDocuments(prev => [...prev, ...newDocuments]);
        setUploading(false);
      }, 2000);
    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
      setUploading(false);
    }
  };

  const handleDeleteDocument = async (documentId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
      return;
    }

    try {
      // TODO: Implémenter la suppression réelle
      // await fetch(`/api/students/me/documents/${documentId}`, {
      //   method: 'DELETE',
      //   headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      // });
      
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'verified': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      'rejected': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />;
      case 'rejected':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <DocumentIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const filteredDocuments = selectedCategory === 'all' 
    ? documents 
    : documents.filter(doc => doc.category === selectedCategory);

  const sortedDocuments = filteredDocuments.sort((a, b) => 
    new Date(b.uploadDate) - new Date(a.uploadDate)
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Upload */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Mes Documents
            </h2>
            <div className="flex items-center space-x-3">
              <input
                type="file"
                id="file-upload"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileUpload}
                className="hidden"
              />
              <label
                htmlFor="file-upload"
                className={cn(
                  'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer',
                  uploading && 'opacity-50 cursor-not-allowed'
                )}
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Upload en cours...
                  </>
                ) : (
                  <>
                    <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                    Ajouter des documents
                  </>
                )}
              </label>
            </div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="px-6 py-4">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.value}
                onClick={() => setSelectedCategory(category.value)}
                className={cn(
                  'px-3 py-1 rounded-full text-sm font-medium transition-colors',
                  selectedCategory === category.value
                    ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                )}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Documents Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedDocuments.map((document) => (
          <div key={document.id} className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div className="p-6">
              {/* Document Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {document.type === 'photo' ? (
                      <img
                        src={document.url}
                        alt={document.name}
                        className="h-10 w-10 rounded object-cover"
                      />
                    ) : (
                      <DocumentTextIcon className="h-10 w-10 text-gray-400" />
                    )}
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {document.name}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {documentTypes[document.type] || document.type}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  {getStatusIcon(document.status)}
                </div>
              </div>

              {/* Document Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Taille:</span>
                  <span className="text-gray-900 dark:text-white">{document.size}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Uploadé:</span>
                  <span className="text-gray-900 dark:text-white">
                    {new Date(document.uploadDate).toLocaleDateString('fr-FR')}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Statut:</span>
                  <span className={cn(
                    'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                    getStatusColor(document.status)
                  )}>
                    {document.status === 'verified' ? 'Vérifié' : 
                     document.status === 'pending' ? 'En attente' : 
                     document.status === 'rejected' ? 'Rejeté' : document.status}
                  </span>
                </div>
              </div>

              {/* Description */}
              {document.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {document.description}
                </p>
              )}

              {/* Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={() => window.open(document.url, '_blank')}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <EyeIcon className="h-4 w-4 mr-1" />
                  Voir
                </button>
                <button
                  onClick={() => handleDeleteDocument(document.id)}
                  className="inline-flex items-center justify-center px-3 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {sortedDocuments.length === 0 && (
        <div className="text-center py-12">
          <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            Aucun document
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {selectedCategory === 'all' 
              ? 'Vous n\'avez pas encore uploadé de documents.'
              : `Aucun document dans la catégorie "${categories.find(c => c.value === selectedCategory)?.label}".`
            }
          </p>
          <div className="mt-6">
            <label
              htmlFor="file-upload-empty"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 cursor-pointer"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Ajouter votre premier document
            </label>
            <input
              type="file"
              id="file-upload-empty"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentDocuments;
