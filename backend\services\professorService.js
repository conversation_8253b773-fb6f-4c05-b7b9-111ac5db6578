const bcrypt = require('bcryptjs');
const User = require('../models/User');
const Professor = require('../models/Professor');
const { sendEmail } = require('./emailService');

/**
 * Générer un mot de passe temporaire sécurisé
 */
const generateTemporaryPassword = () => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
};

/**
 * Créer un professeur avec compte utilisateur et envoi d'email
 */
const createProfessorWithCredentials = async (professorData, createdByUserId) => {
  try {
    console.log('🎓 Création d\'un nouveau professeur...');
    
    // 1. Vérifier si l'email existe déjà
    const existingUser = await User.findOne({ email: professorData.email });
    if (existingUser) {
      throw new Error('Un utilisateur avec cet email existe déjà');
    }

    // 2. Générer un mot de passe temporaire
    const temporaryPassword = generateTemporaryPassword();
    console.log(`🔑 Mot de passe temporaire généré: ${temporaryPassword}`);
    const hashedPassword = await bcrypt.hash(temporaryPassword, 12);

    // 3. Créer le compte utilisateur
    const userData = {
      firstName: professorData.firstName,
      lastName: professorData.lastName,
      email: professorData.email,
      phone: professorData.phone || '',
      password: hashedPassword,
      role: 'professor',
      isActive: true,
      isEmailVerified: true, // Admin crée le compte, donc vérifié
      mustChangePassword: true, // Forcer le changement à la première connexion
      createdBy: createdByUserId
    };

    const user = new User(userData);
    await user.save();

    console.log(`✅ Compte utilisateur créé: ${user._id}`);

    // 4. Créer le profil professeur
    const professorProfile = {
      user: user._id,
      academicTitle: professorData.academicTitle || 'Dr.',
      academicRank: professorData.academicRank,
      department: professorData.department,
      employmentStatus: professorData.employmentStatus || 'full_time',
      contractType: professorData.contractType || 'permanent',
      startDate: professorData.startDate || new Date(),
      
      // Informations salariales (gérées par l'admin)
      salary: {
        amount: professorData.salary?.amount || 0,
        currency: professorData.salary?.currency || 'TND',
        paymentFrequency: professorData.salary?.paymentFrequency || 'monthly'
      },

      // Informations de base
      education: professorData.education || [],
      professionalExperience: professorData.professionalExperience || [],
      teachingExperience: {
        yearsOfTeaching: professorData.yearsOfTeaching || 0,
        teachingAreas: professorData.teachingAreas || [],
        preferredTeachingMethods: professorData.preferredTeachingMethods || []
      },

      // Disponibilité par défaut
      availability: {
        preferredDays: professorData.preferredDays || ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        maxHoursPerWeek: professorData.maxHoursPerWeek || 20,
        canTravelForTeaching: professorData.canTravelForTeaching || false
      },

      // Compétences
      skills: {
        languages: professorData.languages || [],
        technicalSkills: professorData.technicalSkills || [],
        certifications: professorData.certifications || []
      },

      // Profil professionnel
      professionalProfile: {
        bio: professorData.bio || '',
        expertise: professorData.expertise || [],
        consultingAreas: professorData.consultingAreas || []
      },

      // Métadonnées
      createdBy: createdByUserId
    };

    // Générer le numéro d'employé manuellement si nécessaire
    if (!professorProfile.employeeNumber) {
      const year = new Date().getFullYear();
      const count = await Professor.countDocuments({
        employeeNumber: new RegExp(`^PROF${year}`)
      });
      professorProfile.employeeNumber = `PROF${year}${String(count + 1).padStart(4, '0')}`;
      console.log(`🔢 Numéro d'employé généré: ${professorProfile.employeeNumber}`);
    }

    const professor = new Professor(professorProfile);
    await professor.save();

    console.log(`🎓 Profil professeur créé: ${professor._id} (${professor.employeeNumber})`);

    // 5. Envoyer l'email avec les identifiants
    console.log(`📧 Envoi de l'email à ${user.email}...`);
    const emailResult = await sendProfessorCredentialsEmail(user.email, {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      temporaryPassword: temporaryPassword,
      employeeNumber: professor.employeeNumber,
      department: professor.department,
      academicRank: professor.academicRank
    });

    console.log(`📧 Résultat de l'envoi d'email:`, emailResult);

    // 6. Retourner les informations (sans le mot de passe)
    const result = await Professor.findById(professor._id)
      .populate('user', '-password -resetPasswordToken -verificationToken');

    return {
      success: true,
      professor: result,
      message: `Professeur créé avec succès. Email envoyé à ${user.email}`
    };

  } catch (error) {
    console.error('❌ Erreur lors de la création du professeur:', error);
    throw error;
  }
};

/**
 * Envoyer l'email avec les identifiants au nouveau professeur
 */
const sendProfessorCredentialsEmail = async (email, professorInfo) => {
  const subject = 'Bienvenue dans l\'équipe EMBA - Vos identifiants de connexion';
  
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #dc2626, #991b1b); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .credentials-box { background: white; border: 2px solid #dc2626; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .credential-item { margin: 10px 0; padding: 10px; background: #fef2f2; border-radius: 4px; }
        .warning { background: #fef3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🎓 Bienvenue dans l'équipe EMBA</h1>
          <p>Executive MBA - ESPRIT</p>
        </div>
        
        <div class="content">
          <h2>Bonjour ${professorInfo.academicRank} ${professorInfo.firstName} ${professorInfo.lastName},</h2>
          
          <p>Nous sommes ravis de vous accueillir dans l'équipe pédagogique du programme Executive MBA d'ESPRIT.</p>
          
          <p>Votre compte professeur a été créé avec succès. Voici vos informations de connexion :</p>
          
          <div class="credentials-box">
            <h3>🔐 Vos Identifiants de Connexion</h3>
            <div class="credential-item">
              <strong>Email :</strong> ${professorInfo.email}
            </div>
            <div class="credential-item">
              <strong>Mot de passe temporaire :</strong> <code style="background: #e5e7eb; padding: 4px 8px; border-radius: 4px; font-family: monospace;">${professorInfo.temporaryPassword}</code>
            </div>
            <div class="credential-item">
              <strong>Numéro d'employé :</strong> ${professorInfo.employeeNumber}
            </div>
            <div class="credential-item">
              <strong>Département :</strong> ${professorInfo.department}
            </div>
          </div>
          
          <div class="warning">
            <strong>⚠️ Important :</strong>
            <ul>
              <li>Ce mot de passe est temporaire et doit être changé lors de votre première connexion</li>
              <li>Gardez ces informations confidentielles</li>
              <li>Connectez-vous dans les 7 jours pour activer votre compte</li>
            </ul>
          </div>
          
          <div style="text-align: center;">
            <a href="http://localhost:3000/login" class="button">Se connecter au dashboard</a>
          </div>
          
          <h3>📋 Prochaines étapes :</h3>
          <ol>
            <li>Connectez-vous avec vos identifiants</li>
            <li>Changez votre mot de passe temporaire</li>
            <li>Complétez votre profil professeur</li>
            <li>Consultez vos cours assignés</li>
          </ol>
          
          <p>Si vous avez des questions ou besoin d'assistance, n'hésitez pas à contacter l'administration.</p>
          
          <p>Cordialement,<br>
          <strong>L'équipe EMBA - ESPRIT</strong></p>
        </div>
        
        <div class="footer">
          <p>© 2025 Executive MBA - ESPRIT. Tous droits réservés.</p>
          <p>Cet email contient des informations confidentielles.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const textContent = `
Bienvenue dans l'équipe EMBA

Bonjour ${professorInfo.academicRank} ${professorInfo.firstName} ${professorInfo.lastName},

Nous sommes ravis de vous accueillir dans l'équipe pédagogique du programme Executive MBA d'ESPRIT.

Vos identifiants de connexion :
- Email : ${professorInfo.email}
- Mot de passe temporaire : ${professorInfo.temporaryPassword}
- Numéro d'employé : ${professorInfo.employeeNumber}
- Département : ${professorInfo.department}

IMPORTANT : Ce mot de passe est temporaire et doit être changé lors de votre première connexion.

Connectez-vous sur : http://localhost:3000/login

Cordialement,
L'équipe EMBA - ESPRIT
  `;

  const template = {
    subject: subject,
    html: htmlContent
  };

  return await sendEmail(email, template);
};

/**
 * Mettre à jour les informations salariales d'un professeur
 */
const updateProfessorSalary = async (professorId, salaryData, updatedByUserId) => {
  try {
    const professor = await Professor.findByIdAndUpdate(
      professorId,
      {
        'salary.amount': salaryData.amount,
        'salary.currency': salaryData.currency || 'TND',
        'salary.paymentFrequency': salaryData.paymentFrequency || 'monthly',
        updatedBy: updatedByUserId
      },
      { new: true }
    ).populate('user', '-password');

    if (!professor) {
      throw new Error('Professeur non trouvé');
    }

    console.log(`💰 Salaire mis à jour pour le professeur ${professor.employeeNumber}`);
    return professor;
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du salaire:', error);
    throw error;
  }
};

module.exports = {
  createProfessorWithCredentials,
  sendProfessorCredentialsEmail,
  updateProfessorSalary,
  generateTemporaryPassword
};
