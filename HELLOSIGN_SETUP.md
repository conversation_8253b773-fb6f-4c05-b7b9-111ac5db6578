# Configuration HelloSign/Dropbox Sign pour EMBA

## 🎯 Pourquoi HelloSign ?

- **3 documents gratuits par mois** (parfait pour tester)
- **API simple et fiable**
- **Légalement reconnu** dans le monde entier
- **Interface professionnelle**
- **Support excellent**

## 📋 Étapes pour obtenir les clés API

### 1. Créer un compte HelloSign/Dropbox Sign

1. Allez sur [sign.dropbox.com](https://sign.dropbox.com) ou [hellosign.com](https://hellosign.com)
2. Cliquez sur **"Sign up"** ou **"Get started"**
3. Créez votre compte avec votre email professionnel
4. Vérifiez votre email et activez votre compte

### 2. Accéder aux paramètres API

1. Connectez-vous à votre compte HelloSign
2. Cliquez sur votre **profil** (en haut à droite)
3. Sélectionnez **"Settings"** ou **"Paramètres"**
4. <PERSON><PERSON> le menu de gauche, cliquez sur **"API"**

### 3. Créer une application API

1. Dans la section API, cliquez sur **"Create API App"**
2. Remplissez les informations :
   - **App Name**: `EMBA Signature System`
   - **Domain**: `localhost` (pour les tests) ou votre domaine
   - **Callback URL**: `http://localhost:5000/api/applications/hellosign-webhook`
3. Cliquez sur **"Create App"**

### 4. Récupérer vos clés

Après création de l'app, vous obtiendrez :

- **API Key** : Une clé longue commençant par des lettres/chiffres
- **Client ID** : Un identifiant unique pour votre application

**⚠️ Important :** Gardez ces clés secrètes !

## 🔧 Configuration dans votre projet

### 1. Mettre à jour le fichier `.env`

```env
# HelloSign/Dropbox Sign Configuration
SIGNATURE_METHOD=hellosign
HELLOSIGN_API_KEY=votre_api_key_ici
HELLOSIGN_CLIENT_ID=votre_client_id_ici
HELLOSIGN_WEBHOOK_URL=http://localhost:5000/api/applications/hellosign-webhook
```

### 2. Exemple de clés (format)

```env
# Exemple (ne pas utiliser ces valeurs)
HELLOSIGN_API_KEY=1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t
HELLOSIGN_CLIENT_ID=abcd1234efgh5678ijkl9012
```

## 🧪 Test de la configuration

### 1. Redémarrer le serveur

```bash
cd backend
npm run dev
```

### 2. Vérifier les logs

Vous devriez voir :
```
📝 Utilisation de HelloSign/Dropbox Sign
```

### 3. Tester une candidature

1. Soumettez une candidature depuis le formulaire
2. Vérifiez que vous recevez un email de HelloSign
3. Signez le document
4. Vérifiez que le statut est mis à jour

## 🔍 Dépannage

### Erreur "API Key invalide"

- Vérifiez que la clé API est correcte
- Assurez-vous qu'il n'y a pas d'espaces avant/après
- Vérifiez que l'app API est activée

### Pas d'email reçu

- Vérifiez votre dossier spam
- Assurez-vous que l'email du candidat est correct
- Vérifiez les logs du serveur pour les erreurs

### Webhook ne fonctionne pas

- En local, utilisez ngrok pour exposer votre serveur
- Mettez à jour l'URL webhook dans HelloSign
- Vérifiez que l'endpoint répond correctement

## 🚀 Passage en production

### 1. Domaine personnalisé

Remplacez `localhost` par votre domaine :

```env
HELLOSIGN_WEBHOOK_URL=https://votre-domaine.com/api/applications/hellosign-webhook
```

### 2. Mise à jour de l'app HelloSign

1. Retournez dans les paramètres API HelloSign
2. Modifiez votre app pour utiliser le vrai domaine
3. Mettez à jour l'URL de callback

### 3. Plan payant (si nécessaire)

Si vous dépassez 3 documents/mois :
- HelloSign Pro : $15/mois pour 40 documents
- HelloSign Business : $25/mois pour 100 documents

## 📞 Support

- **Documentation HelloSign** : [developers.hellosign.com](https://developers.hellosign.com)
- **Support HelloSign** : <EMAIL>
- **Status page** : [status.hellosign.com](https://status.hellosign.com)

## ✅ Checklist finale

- [ ] Compte HelloSign créé et vérifié
- [ ] App API créée avec les bonnes URLs
- [ ] Clés API copiées dans `.env`
- [ ] Serveur redémarré
- [ ] Test de candidature effectué
- [ ] Email de signature reçu et testé
- [ ] Webhook configuré et testé

Une fois cette checklist complète, votre système de signature électronique HelloSign sera opérationnel ! 🎉
