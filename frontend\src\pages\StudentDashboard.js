import React, { useState, useEffect } from 'react';
import {
  UserIcon,
  AcademicCapIcon,
  DocumentTextIcon,
  CalendarIcon,
  ChartBarIcon,
  CogIcon,
  BookOpenIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  TrophyIcon,
  StarIcon,
  FireIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { cn } from '../utils/cn';
import StudentProfile from '../components/StudentProfile';
import StudentCourses from '../components/StudentCourses';
import StudentDocuments from '../components/StudentDocuments';
import UserMenu from '../components/UserMenu';
import DashboardThemeToggle from '../components/DashboardThemeToggle';
import NotificationBell from '../components/NotificationBell';
import ChangePassword from '../components/ChangePassword';

// Gamification Components
import LevelProgress from '../components/gamification/LevelProgress';
import BadgeCollection from '../components/gamification/BadgeCollection';
import AchievementCard from '../components/gamification/AchievementCard';
import ActivityFeed from '../components/gamification/ActivityFeed';
import CourseCard from '../components/gamification/CourseCard';

const StudentDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [error, setError] = useState(null);

  // Load dashboard data from API
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5000/api/students/me/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des données');
      }

      const result = await response.json();
      setDashboardData(result.data);
    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Load courses data from API
  const loadCoursesData = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/students/me/courses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Erreur lors du chargement des cours');
      }

      const result = await response.json();
      setCourses(result.data);
    } catch (error) {
      console.error('Erreur lors du chargement des cours:', error);
    }
  };

  useEffect(() => {
    loadDashboardData();
    loadCoursesData();
  }, []);

  const getStatusColor = (status) => {
    const colors = {
      'Active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'Inactive': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      'Suspended': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    };
    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'high': 'text-red-600 dark:text-red-400',
      'medium': 'text-yellow-600 dark:text-yellow-400',
      'low': 'text-green-600 dark:text-green-400'
    };
    return colors[priority] || 'text-gray-600 dark:text-gray-400';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'pending':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const tabs = [
    { id: 'overview', name: 'Dashboard', icon: TrophyIcon},
    { id: 'courses', name: 'Mes Cours', icon: BookOpenIcon},
    { id: 'achievements', name: 'Succès', icon: StarIcon},
    { id: 'profile', name: 'Profil', icon: UserIcon},
    { id: 'documents', name: 'Documents', icon: DocumentTextIcon}
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Chargement de votre dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Erreur de chargement</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => {
              loadDashboardData();
              loadCoursesData();
            }}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">📚</div>
          <p className="text-gray-600 dark:text-gray-400">Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
      {/* Gamified Header */}
      <div className="bg-white dark:bg-gray-800 shadow-lg border-b-4 border-gradient-to-r from-purple-500 to-pink-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-16 w-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                  {dashboardData.gamification.level}
                </div>
              </div>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                  🎯 Bonjour, {user?.firstName} {user?.lastName}
                  <span className="ml-2 text-lg">🔥</span>
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {dashboardData.profile.studentNumber} • {dashboardData.profile.program}
                </p>
                <div className="flex items-center mt-1 space-x-4">
                  <span className="text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400 px-2 py-1 rounded-full">
                    Niveau {dashboardData.gamification.level}
                  </span>
                  <span className="text-xs bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 px-2 py-1 rounded-full">
                    {dashboardData.gamification.totalPoints} points
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Theme Toggle */}
              <DashboardThemeToggle />

              {/* Notifications */}
              <NotificationBell />

              {/* User Menu */}
              <UserMenu
                onProfileClick={() => setActiveTab('profile')}
                onChangePasswordClick={() => setShowChangePassword(true)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Gamified Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    'flex items-center py-4 px-4 border-b-3 font-medium text-sm transition-all duration-200 rounded-t-lg',
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 shadow-sm'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-700/50'
                  )}
                >
                  <span className="text-lg mr-2">{tab.emoji}</span>
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Level Progress - Hero Section */}
            <LevelProgress
              level={dashboardData.gamification.level}
              xpCurrent={dashboardData.gamification.xpCurrent}
              xpRequired={dashboardData.gamification.xpRequired}
              totalPoints={dashboardData.gamification.totalPoints}
            />

            {/* Achievements Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {dashboardData.gamification.achievements.map((achievement) => (
                <AchievementCard key={achievement.id} achievement={achievement} />
              ))}
            </div>

            {/* Badges and Activity Feed */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <BadgeCollection badges={dashboardData.gamification.badges} />
              <ActivityFeed activities={dashboardData.gamification.recentActivities} />
            </div>
          </div>
        )}

        {activeTab === 'courses' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                📚 Mes Cours
              </h2>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {courses.length} cours actifs
              </span>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {courses.map((course) => (
                <CourseCard key={course.id} course={course} />
              ))}
            </div>
          </div>
        )}

        {activeTab === 'achievements' && (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                🏆 Vos Succès
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Découvrez tous vos accomplissements et badges obtenus
              </p>
            </div>

            {/* All Achievements */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dashboardData.gamification.achievements.map((achievement) => (
                <AchievementCard key={achievement.id} achievement={achievement} />
              ))}
            </div>

            {/* All Badges */}
            <BadgeCollection badges={dashboardData.gamification.badges} />
          </div>
        )}

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <StudentProfile />
        )}

        {/* Documents Tab */}
        {activeTab === 'documents' && (
          <StudentDocuments />
        )}

        {/* Other tabs content */}
        {!['overview', 'courses', 'achievements', 'profile', 'documents'].includes(activeTab) && (
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="text-center py-12">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <DocumentTextIcon />
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                Contenu à venir
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Cette section sera implémentée dans les prochaines étapes.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Change Password Modal */}
      {showChangePassword && (
        <ChangePassword onClose={() => setShowChangePassword(false)} />
      )}
    </div>
  );
};

export default StudentDashboard;
