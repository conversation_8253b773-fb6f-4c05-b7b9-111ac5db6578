const express = require('express');
const router = express.Router();
const Interview = require('../models/Interview');
const Application = require('../models/Application');
const User = require('../models/User');
const { authenticate, authorize } = require('../middleware/auth');
const { sendInterviewEmail } = require('../services/emailService');

// GET /api/interviews - Obtenir tous les entretiens
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 10, status, date, interviewer } = req.query;
    const query = {};

    if (status) query.status = status;
    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);
      query.scheduledDate = { $gte: startDate, $lt: endDate };
    }
    if (interviewer) query['interviewers.interviewer'] = interviewer;

    const interviews = await Interview.find(query)
      .populate('application', 'applicationNumber personalInfo')
      .populate('interviewers.interviewer', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ scheduledDate: 1 });

    const total = await Interview.countDocuments(query);

    res.json({
      interviews,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/interviews/:id - Obtenir un entretien spécifique
router.get('/:id', authenticate, async (req, res) => {
  try {
    const interview = await Interview.findById(req.params.id)
      .populate('application')
      .populate('interviewers.interviewer', 'firstName lastName email')
      .populate('createdBy', 'firstName lastName')
      .populate('evaluation.completedBy', 'firstName lastName');

    if (!interview) {
      return res.status(404).json({ message: 'Entretien non trouvé' });
    }

    res.json(interview);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/interviews - Créer un nouvel entretien
router.post('/', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    const {
      applicationId,
      scheduledDate,
      duration,
      location,
      meetingLink,
      meetingRoom,
      interviewers
    } = req.body;

    // Vérifier que la candidature existe
    const application = await Application.findById(applicationId);
    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    // Créer l'entretien
    const interview = new Interview({
      application: applicationId,
      scheduledDate: new Date(scheduledDate),
      duration: duration || 60,
      location: location || 'video_call',
      meetingLink,
      meetingRoom,
      interviewers: interviewers || [{ interviewer: req.user.id, role: 'primary' }],
      createdBy: req.user.id
    });

    await interview.save();

    // Mettre à jour le statut de la candidature
    application.applicationStatus.status = 'interview_scheduled';
    application.interview = {
      scheduled: true,
      scheduledDate: interview.scheduledDate,
      interviewId: interview._id
    };
    await application.save();

    // Envoyer notification email au candidat
    try {
      await sendInterviewEmail(
        application.personalInfo.email,
        application.personalInfo.firstName,
        interview.scheduledDate,
        interview.location,
        interview.meetingLink || interview.meetingRoom
      );
      
      await interview.notifyCandidate();
    } catch (emailError) {
      console.error('❌ Erreur envoi email entretien:', emailError);
    }

    // Populer les données pour la réponse
    await interview.populate('application', 'applicationNumber personalInfo');
    await interview.populate('interviewers.interviewer', 'firstName lastName email');

    res.status(201).json({
      success: true,
      message: 'Entretien programmé avec succès',
      interview
    });

  } catch (error) {
    console.error('❌ Erreur création entretien:', error);
    res.status(400).json({ message: error.message });
  }
});

// PUT /api/interviews/:id - Modifier un entretien
router.put('/:id', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    const interview = await Interview.findById(req.params.id);
    if (!interview) {
      return res.status(404).json({ message: 'Entretien non trouvé' });
    }

    // Mettre à jour les champs
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        interview[key] = req.body[key];
      }
    });

    interview.updatedBy = req.user.id;
    await interview.save();

    await interview.populate('application', 'applicationNumber personalInfo');
    await interview.populate('interviewers.interviewer', 'firstName lastName email');

    res.json({
      success: true,
      message: 'Entretien mis à jour avec succès',
      interview
    });

  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// POST /api/interviews/:id/evaluate - Évaluer un entretien
router.post('/:id/evaluate', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    const interview = await Interview.findById(req.params.id);
    if (!interview) {
      return res.status(404).json({ message: 'Entretien non trouvé' });
    }

    const {
      scores,
      recommendation,
      strengths,
      concerns,
      generalComments,
      questionsAsked,
      candidateResponses
    } = req.body;

    // Mettre à jour l'évaluation
    interview.evaluation = {
      ...interview.evaluation,
      scores: scores || interview.evaluation.scores,
      recommendation,
      strengths: strengths || [],
      concerns: concerns || [],
      generalComments,
      questionsAsked: questionsAsked || [],
      candidateResponses
    };

    // Marquer comme terminé
    await interview.complete(req.user.id);

    // Mettre à jour le statut de la candidature
    const application = await Application.findById(interview.application);
    if (application) {
      application.applicationStatus.status = 'interview_completed';
      application.interview.completed = true;
      application.interview.completedDate = new Date();
      application.interview.evaluation = {
        averageScore: interview.evaluation.averageScore,
        recommendation: interview.evaluation.recommendation
      };
      await application.save();
    }

    await interview.populate('application', 'applicationNumber personalInfo');

    res.json({
      success: true,
      message: 'Évaluation enregistrée avec succès',
      interview
    });

  } catch (error) {
    console.error('❌ Erreur évaluation entretien:', error);
    res.status(400).json({ message: error.message });
  }
});

// POST /api/interviews/:id/cancel - Annuler un entretien
router.post('/:id/cancel', authenticate, authorize(['admin', 'staff']), async (req, res) => {
  try {
    const { reason } = req.body;
    const interview = await Interview.findById(req.params.id);
    
    if (!interview) {
      return res.status(404).json({ message: 'Entretien non trouvé' });
    }

    interview.status = 'cancelled';
    interview.notes = reason ? `Annulé: ${reason}` : 'Entretien annulé';
    interview.updatedBy = req.user.id;
    await interview.save();

    // Mettre à jour la candidature
    const application = await Application.findById(interview.application);
    if (application) {
      application.interview.cancelled = true;
      application.interview.cancelReason = reason;
      await application.save();
    }

    res.json({
      success: true,
      message: 'Entretien annulé avec succès'
    });

  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/interviews/calendar/:month/:year - Obtenir le calendrier des entretiens
router.get('/calendar/:month/:year', authenticate, async (req, res) => {
  try {
    const { month, year } = req.params;
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    const interviews = await Interview.find({
      scheduledDate: { $gte: startDate, $lte: endDate }
    })
    .populate('application', 'applicationNumber personalInfo')
    .populate('interviewers.interviewer', 'firstName lastName')
    .sort({ scheduledDate: 1 });

    res.json({ interviews });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
