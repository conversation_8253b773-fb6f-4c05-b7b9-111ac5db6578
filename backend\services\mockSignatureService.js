const crypto = require('crypto');

class MockSignatureService {
  constructor() {
    this.BASE_URL = 'http://localhost:5000'; // URL locale pour simulation
  }

  // Simuler le processus de signature DocuSeal
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🔏 Simulation du processus de signature pour:', applicationData.email);
      
      // Générer des IDs simulés
      const submissionId = this.generateMockId('sub');
      const templateId = this.generateMockId('tpl');
      
      // Simuler un délai de traitement
      await this.delay(1000);
      
      console.log('✅ Simulation réussie - IDs générés:', { submissionId, templateId });
      
      return {
        success: true,
        submissionId,
        templateId,
        message: 'Demande de signature simulée envoyée avec succès',
        signatureUrl: `${this.BASE_URL}/signature/${submissionId}`,
        mockMode: true
      };
      
    } catch (error) {
      console.error('❌ Erreur simulation signature:', error);
      return {
        success: false,
        error: error.message,
        mockMode: true
      };
    }
  }

  // Obtenir le statut d'une soumission (simulation)
  async getSubmissionStatus(submissionId) {
    console.log('📋 Simulation - Statut de la soumission:', submissionId);
    
    return {
      id: submissionId,
      status: 'pending',
      created_at: new Date().toISOString(),
      submitters: [
        {
          status: 'pending',
          email: '<EMAIL>'
        }
      ],
      mockMode: true
    };
  }

  // Télécharger le document signé (simulation)
  async downloadSignedDocument(submissionId) {
    console.log('📥 Simulation - Téléchargement document signé:', submissionId);
    
    // Retourner un buffer vide pour simulation
    return Buffer.from('Document signé simulé');
  }

  // Générer un ID unique pour simulation
  generateMockId(prefix = 'mock') {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');
    return `${prefix}_${timestamp}_${random}`;
  }

  // Délai pour simulation
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Créer une URL de signature publique (simulation)
  createPublicSignatureUrl(submissionId, applicationData) {
    const signatureToken = crypto
      .createHash('sha256')
      .update(`${submissionId}-${applicationData.email}-${Date.now()}`)
      .digest('hex')
      .substring(0, 32);
    
    return `${this.BASE_URL}/api/signature/sign/${submissionId}?token=${signatureToken}`;
  }

  // Valider une signature (simulation)
  async validateSignature(submissionId, signatureData) {
    console.log('✅ Simulation - Validation signature:', submissionId);
    
    return {
      valid: true,
      signedAt: new Date().toISOString(),
      signerInfo: {
        email: signatureData.email,
        name: signatureData.name,
        ip: signatureData.ip || '127.0.0.1'
      },
      mockMode: true
    };
  }
}

module.exports = new MockSignatureService();
