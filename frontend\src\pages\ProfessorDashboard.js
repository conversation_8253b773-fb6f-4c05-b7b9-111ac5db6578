import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  AcademicCapIcon,
  BookOpenIcon,
  CalendarDaysIcon,
  ClockIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  BellIcon,
  CurrencyDollarIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const ProfessorDashboard = () => {
  const { user } = useAuth();
  const [professorData, setProfessorData] = useState(null);
  const [stats, setStats] = useState(null);
  const [recentActivities, setRecentActivities] = useState([]);
  const [upcomingClasses, setUpcomingClasses] = useState([]);
  const [loading, setLoading] = useState(true);

  // Charger les données du professeur
  const fetchProfessorData = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/profile', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfessorData(data.professor);
      }
    } catch (error) {
      console.error('Erreur lors du chargement du profil professeur:', error);
    }
  };

  // Charger les statistiques
  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/dashboard/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  // Charger les cours à venir
  const fetchUpcomingClasses = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/professors/dashboard/upcoming-classes', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUpcomingClasses(data.classes);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des cours à venir:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchProfessorData(),
        fetchStats(),
        fetchUpcomingClasses()
      ]);
      setLoading(false);
    };

    loadData();
  }, []);

  // Données par défaut si pas encore chargées
  const defaultStats = [
    {
      title: 'Cours Assignés',
      value: stats?.totalCourses || '0',
      change: 'Cette session',
      changeType: 'neutral',
      icon: BookOpenIcon,
      color: 'primary'
    },
    {
      title: 'Étudiants Total',
      value: stats?.totalStudents || '0',
      change: 'Tous cours confondus',
      changeType: 'neutral',
      icon: UserGroupIcon,
      color: 'success'
    },
    {
      title: 'Heures/Semaine',
      value: stats?.weeklyHours || '0',
      change: 'Charge actuelle',
      changeType: 'neutral',
      icon: ClockIcon,
      color: 'info'
    },
    {
      title: 'Note Moyenne',
      value: stats?.averageRating || '0.0',
      change: 'Évaluations étudiants',
      changeType: 'neutral',
      icon: StarIcon,
      color: 'warning'
    }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête de bienvenue */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Bonjour, {professorData?.academicTitle} {user?.firstName} {user?.lastName}
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              {professorData?.department} • {professorData?.academicRank}
            </p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
              Numéro d'employé: {professorData?.employeeNumber}
            </p>
          </div>
          <div className="flex-shrink-0">
            <div className="h-16 w-16 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
              <AcademicCapIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {defaultStats.map((stat, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`flex-shrink-0 p-3 rounded-lg bg-${stat.color}-100 dark:bg-${stat.color}-900`}>
                <stat.icon className={`h-6 w-6 text-${stat.color}-600 dark:text-${stat.color}-400`} />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {stat.title}
                </p>
                <div className="flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {stat.change}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Contenu principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Cours à venir */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <CalendarDaysIcon className="h-5 w-5 mr-2 text-primary-600" />
                Cours à Venir
              </h2>
            </div>
            <div className="p-6">
              {upcomingClasses.length > 0 ? (
                <div className="space-y-4">
                  {upcomingClasses.map((classItem, index) => (
                    <div key={index} className="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-lg bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                          <BookOpenIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                        </div>
                      </div>
                      <div className="ml-4 flex-1">
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                          {classItem.courseName}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {classItem.time} • Salle {classItem.room}
                        </p>
                        <p className="text-xs text-gray-400 dark:text-gray-500">
                          {classItem.studentCount} étudiants
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          {classItem.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CalendarDaysIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                    Aucun cours à venir
                  </h3>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Votre planning est libre pour le moment.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions rapides */}
        <div className="space-y-6">
          {/* Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Actions Rapides
              </h2>
            </div>
            <div className="p-6 space-y-3">
              <button className="w-full flex items-center px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <BookOpenIcon className="h-5 w-5 mr-3 text-primary-600" />
                Mes Cours
              </button>
              <button className="w-full flex items-center px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <UserGroupIcon className="h-5 w-5 mr-3 text-green-600" />
                Mes Étudiants
              </button>
              <button className="w-full flex items-center px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <ChartBarIcon className="h-5 w-5 mr-3 text-blue-600" />
                Notes & Évaluations
              </button>
              <button className="w-full flex items-center px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <DocumentTextIcon className="h-5 w-5 mr-3 text-purple-600" />
                Documents
              </button>
              <button className="w-full flex items-center px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <CalendarDaysIcon className="h-5 w-5 mr-3 text-orange-600" />
                Planning
              </button>
            </div>
          </div>

          {/* Notifications récentes */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <BellIcon className="h-5 w-5 mr-2 text-primary-600" />
                Notifications
              </h2>
            </div>
            <div className="p-6">
              <div className="text-center py-4">
                <BellIcon className="mx-auto h-8 w-8 text-gray-400" />
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Aucune notification récente
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessorDashboard;
