import React, { useState, useEffect } from 'react';
import {
  EyeIcon,
  CheckIcon,
  XMarkIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  PhoneIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  UserIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { cn } from '../utils/cn';
import Modal from '../components/Modal';

const Applications = () => {
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [actionLoading, setActionLoading] = useState(null);
  const [showInterviewModal, setShowInterviewModal] = useState(false);
  const [interviewData, setInterviewData] = useState({
    scheduledDate: '',
    duration: 60,
    location: 'video_call',
    meetingLink: '',
    meetingRoom: ''
  });

  // Charger les candidatures
  const loadApplications = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('http://localhost:5000/api/applications');
      const data = await response.json();
      
      if (response.ok) {
        setApplications(data.applications || []);
      } else {
        throw new Error(data.message || 'Erreur lors du chargement');
      }
    } catch (err) {
      console.error('Erreur lors du chargement des candidatures:', err);
      setError('Impossible de charger les candidatures. Vérifiez votre connexion.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadApplications();
  }, []);

  // Filtrer les candidatures
  const filteredApplications = applications.filter(app => {
    const matchesSearch =
      app.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.applicationNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || app.applicationStatus?.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Actions sur les candidatures
  const handleAction = async (applicationId, action, reason = '') => {
    setActionLoading(applicationId);
    try {
      const response = await fetch(`http://localhost:5000/api/applications/${applicationId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: action,
          reason: reason
        })
      });

      if (response.ok) {
        await loadApplications(); // Recharger les données
        alert(`Candidature ${action === 'accepted' ? 'acceptée' : action === 'rejected' ? 'refusée' : 'mise à jour'} avec succès !`);
        
        // Si acceptée, créer le compte étudiant
        if (action === 'accepted') {
          await createStudentAccount(applicationId);
        }
      } else {
        throw new Error('Erreur lors de la mise à jour');
      }
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur lors de la mise à jour de la candidature');
    } finally {
      setActionLoading(null);
    }
  };

  // Créer un compte étudiant automatiquement
  const createStudentAccount = async (applicationId) => {
    try {
      const response = await fetch(`http://localhost:5000/api/applications/${applicationId}/create-student`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Compte étudiant créé ! Email envoyé à ${result.email} avec les identifiants.`);
      }
    } catch (error) {
      console.error('Erreur lors de la création du compte étudiant:', error);
    }
  };

  // Ouvrir les détails d'une candidature
  const openApplicationDetails = (application) => {
    setSelectedApplication(application);
    setIsModalOpen(true);
  };

  // Programmer un entretien
  const scheduleInterview = (application) => {
    setSelectedApplication(application);
    setShowInterviewModal(true);
    // Pré-remplir avec une date par défaut (demain à 14h)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);
    setInterviewData({
      ...interviewData,
      scheduledDate: tomorrow.toISOString().slice(0, 16)
    });
  };

  // Soumettre la programmation d'entretien
  const handleScheduleInterview = async () => {
    if (!selectedApplication || !interviewData.scheduledDate) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    setActionLoading(selectedApplication._id);
    try {
      const response = await fetch('http://localhost:5000/api/interviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          applicationId: selectedApplication._id,
          ...interviewData
        })
      });

      if (response.ok) {
        await loadApplications();
        setShowInterviewModal(false);
        setInterviewData({
          scheduledDate: '',
          duration: 60,
          location: 'video_call',
          meetingLink: '',
          meetingRoom: ''
        });
        alert('Entretien programmé avec succès ! Le candidat a été notifié par email.');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la programmation');
      }
    } catch (error) {
      console.error('Erreur programmation entretien:', error);
      alert('Erreur lors de la programmation de l\'entretien: ' + error.message);
    } finally {
      setActionLoading(null);
    }
  };

  // Statut avec couleurs
  const getStatusBadge = (status) => {
    const statusConfig = {
      'submitted': { label: 'Soumise', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' },
      'under_review': { label: 'En révision', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' },
      'interview_scheduled': { label: 'Entretien programmé', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' },
      'accepted': { label: 'Acceptée', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' },
      'rejected': { label: 'Refusée', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' },
      'waitlisted': { label: 'Liste d\'attente', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300' }
    };

    const config = statusConfig[status] || statusConfig['submitted'];
    return (
      <span className={cn('px-2 py-1 text-xs font-medium rounded-full', config.color)}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4">
        <p className="text-red-800 dark:text-red-200">{error}</p>
        <button 
          onClick={loadApplications}
          className="mt-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Candidatures</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Gérez les candidatures au programme EMBA ({filteredApplications.length} candidatures)
          </p>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher par nom, email ou numéro..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        
        <div className="relative">
          <FunnelIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="all">Tous les statuts</option>
            <option value="submitted">Soumises</option>
            <option value="under_review">En révision</option>
            <option value="interview_scheduled">Entretien programmé</option>
            <option value="accepted">Acceptées</option>
            <option value="rejected">Refusées</option>
            <option value="waitlisted">Liste d'attente</option>
          </select>
        </div>
      </div>

      {/* Liste des candidatures */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg overflow-hidden">
        {filteredApplications.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm || statusFilter !== 'all' ? 'Aucune candidature trouvée avec ces critères' : 'Aucune candidature pour le moment'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Candidat
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Contact
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date de soumission
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredApplications.map((application) => (
                  <tr key={application._id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-800 flex items-center justify-center">
                            <span className="text-sm font-medium text-primary-600 dark:text-primary-300">
                              {application.firstName?.[0]}{application.lastName?.[0]}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {application.firstName} {application.lastName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            #{application.applicationNumber}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {application.email}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {application.phone}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(application.applicationStatus?.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(application.applicationStatus?.submissionDate).toLocaleDateString('fr-FR')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => openApplicationDetails(application)}
                          className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                          title="Voir les détails"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </button>
                        <button
                              onClick={() => handleAction(application._id, 'accepted')}
                              disabled={actionLoading === application._id}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 disabled:opacity-50"
                              title="Accepter"
                            >
                              <CheckIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => {
                                const reason = prompt('Raison du refus (optionnel):');
                                if (reason !== null) {
                                  handleAction(application._id, 'rejected', reason);
                                }
                              }}
                              disabled={actionLoading === application._id}
                              className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                              title="Refuser"
                            >
                              <XMarkIcon className="h-5 w-5" />
                            </button>
                        
                        {application.applicationStatus?.status === 'submitted' && (
                          <>
                            
                            <button
                              onClick={() => scheduleInterview(application)}
                              disabled={actionLoading === application._id}
                              className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300 disabled:opacity-50"
                              title="Programmer un entretien"
                            >
                              <CalendarDaysIcon className="h-5 w-5" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal des détails */}
      {selectedApplication && (
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title={`Candidature #${selectedApplication.applicationNumber}`}
          size="xl"
        >
          <ApplicationDetails application={selectedApplication} />
        </Modal>
      )}

      {/* Modal de programmation d'entretien */}
      {selectedApplication && (
        <Modal
          isOpen={showInterviewModal}
          onClose={() => setShowInterviewModal(false)}
          title={`Programmer un entretien - ${selectedApplication.firstName} ${selectedApplication.lastName}`}
          size="lg"
        >
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date et heure */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date et heure *
                </label>
                <input
                  type="datetime-local"
                  value={interviewData.scheduledDate}
                  onChange={(e) => setInterviewData({...interviewData, scheduledDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
              </div>

              {/* Durée */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Durée (minutes)
                </label>
                <input
                  type="number"
                  value={interviewData.duration}
                  onChange={(e) => setInterviewData({...interviewData, duration: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="15"
                  max="180"
                />
              </div>
            </div>

            {/* Type d'entretien */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Type d'entretien
              </label>
              <select
                value={interviewData.location}
                onChange={(e) => setInterviewData({...interviewData, location: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="video_call">Visioconférence</option>
                <option value="in_person">En présentiel</option>
                <option value="phone">Téléphone</option>
              </select>
            </div>

            {/* Lien de réunion ou salle */}
            {interviewData.location === 'video_call' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Lien de la réunion
                </label>
                <input
                  type="url"
                  value={interviewData.meetingLink}
                  onChange={(e) => setInterviewData({...interviewData, meetingLink: e.target.value})}
                  placeholder="https://zoom.us/j/..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            )}

            {interviewData.location === 'in_person' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Salle de réunion
                </label>
                <input
                  type="text"
                  value={interviewData.meetingRoom}
                  onChange={(e) => setInterviewData({...interviewData, meetingRoom: e.target.value})}
                  placeholder="Salle A101, Bureau du directeur..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            )}

            {/* Boutons d'action */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setShowInterviewModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={handleScheduleInterview}
                disabled={actionLoading === selectedApplication?._id || !interviewData.scheduledDate}
                className="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {actionLoading === selectedApplication?._id ? 'Programmation...' : 'Programmer l\'entretien'}
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

// Composant Timeline pour suivre l'état de la candidature
const ApplicationTimeline = ({ application }) => {
  const getTimelineEvents = () => {
    const events = [];

    // Événement de soumission
    if (application.createdAt) {
      events.push({
        id: 'submitted',
        title: 'Candidature soumise',
        description: 'Le candidat a soumis sa candidature',
        date: application.createdAt,
        status: 'completed',
        icon: DocumentTextIcon,
        color: 'blue'
      });
    }

    // Événements basés sur l'historique des statuts
    if (application.applicationStatus?.statusHistory) {
      application.applicationStatus.statusHistory.forEach((history, index) => {
        let title, description, color, icon;

        switch (history.status) {
          case 'under_review':
            title = 'En cours d\'examen';
            description = 'La candidature est en cours d\'examen par l\'équipe';
            color = 'yellow';
            icon = ClockIcon;
            break;
          case 'interview_scheduled':
            title = 'Entretien programmé';
            description = 'Un entretien a été programmé avec le candidat';
            color = 'purple';
            icon = CalendarDaysIcon;
            break;
          case 'interview_completed':
            title = 'Entretien terminé';
            description = 'L\'entretien avec le candidat a été terminé';
            color = 'indigo';
            icon = CheckCircleIcon;
            break;
          case 'accepted':
            title = 'Candidature acceptée';
            description = 'La candidature a été acceptée';
            color = 'green';
            icon = CheckCircleIcon;
            break;
          case 'rejected':
            title = 'Candidature refusée';
            description = history.reason || 'La candidature a été refusée';
            color = 'red';
            icon = XMarkIcon;
            break;
          case 'waitlisted':
            title = 'Mise en liste d\'attente';
            description = 'Le candidat a été mis en liste d\'attente';
            color = 'orange';
            icon = ClockIcon;
            break;
          default:
            title = history.status;
            description = history.comments || '';
            color = 'gray';
            icon = InformationCircleIcon;
        }

        events.push({
          id: `status_${index}`,
          title,
          description,
          date: history.changeDate,
          status: 'completed',
          icon,
          color,
          changedBy: history.changedBy,
          reason: history.reason,
          comments: history.comments
        });
      });
    }

    // Événement de signature
    if (application.signature) {
      let signatureTitle, signatureDescription, signatureColor, signatureIcon;

      switch (application.signature.status) {
        case 'pending':
          signatureTitle = 'Signature demandée';
          signatureDescription = 'Une demande de signature a été envoyée au candidat';
          signatureColor = 'yellow';
          signatureIcon = ClockIcon;
          break;
        case 'signed':
          signatureTitle = 'Document signé';
          signatureDescription = 'Le candidat a signé le document';
          signatureColor = 'green';
          signatureIcon = CheckCircleIcon;
          break;
        case 'declined':
          signatureTitle = 'Signature refusée';
          signatureDescription = 'Le candidat a refusé de signer';
          signatureColor = 'red';
          signatureIcon = XMarkIcon;
          break;
        default:
          signatureTitle = 'Signature';
          signatureDescription = application.signature.status;
          signatureColor = 'gray';
          signatureIcon = DocumentTextIcon;
      }

      events.push({
        id: 'signature',
        title: signatureTitle,
        description: signatureDescription,
        date: application.signature.signedAt || application.signature.requestedAt,
        status: application.signature.status === 'signed' ? 'completed' : 'pending',
        icon: signatureIcon,
        color: signatureColor
      });
    }

    // Trier par date
    return events.sort((a, b) => new Date(a.date) - new Date(b.date));
  };

  const events = getTimelineEvents();

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-500 text-white',
      green: 'bg-green-500 text-white',
      red: 'bg-red-500 text-white',
      yellow: 'bg-yellow-500 text-white',
      purple: 'bg-purple-500 text-white',
      indigo: 'bg-indigo-500 text-white',
      orange: 'bg-orange-500 text-white',
      gray: 'bg-gray-500 text-white'
    };
    return colors[color] || colors.gray;
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
        <ClockIcon className="h-5 w-5 mr-2" />
        Timeline de la candidature
      </h3>

      <div className="flow-root">
        <ul className="-mb-8">
          {events.map((event, eventIdx) => {
            const Icon = event.icon;
            return (
              <li key={event.id}>
                <div className="relative pb-8">
                  {eventIdx !== events.length - 1 ? (
                    <span
                      className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200 dark:bg-gray-600"
                      aria-hidden="true"
                    />
                  ) : null}
                  <div className="relative flex space-x-3">
                    <div>
                      <span className={cn(
                        'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white dark:ring-gray-800',
                        getColorClasses(event.color)
                      )}>
                        <Icon className="h-4 w-4" aria-hidden="true" />
                      </span>
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {event.title}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {event.description}
                        </p>
                        {event.reason && (
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            Raison: {event.reason}
                          </p>
                        )}
                      </div>
                      <div className="text-right text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                        <time dateTime={event.date}>
                          {new Date(event.date).toLocaleDateString('fr-FR', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </time>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

// Composant pour visualiser un document
const DocumentViewer = ({ document, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const getDocumentUrl = (doc) => {
    return `http://localhost:5000/api/applications/documents/${doc.filename}`;
  };

  const isPDF = (doc) => {
    return doc.mimeType === 'application/pdf' || doc.filename.toLowerCase().endsWith('.pdf');
  };

  const isImage = (doc) => {
    return doc.mimeType?.startsWith('image/') || /\.(jpg|jpeg|png|gif)$/i.test(doc.filename);
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl max-h-full w-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {document.originalName}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-4 overflow-auto">
          {isPDF(document) ? (
            <iframe
              src={getDocumentUrl(document)}
              className="w-full h-96 border border-gray-300 dark:border-gray-600 rounded"
              title={document.originalName}
              onLoad={() => setLoading(false)}
              onError={() => {
                setLoading(false);
                setError('Impossible de charger le document');
              }}
            />
          ) : isImage(document) ? (
            <img
              src={getDocumentUrl(document)}
              alt={document.originalName}
              className="max-w-full h-auto mx-auto"
              onLoad={() => setLoading(false)}
              onError={() => {
                setLoading(false);
                setError('Impossible de charger l\'image');
              }}
            />
          ) : (
            <div className="text-center py-8">
              <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                Aperçu non disponible pour ce type de fichier
              </p>
              <a
                href={getDocumentUrl(document)}
                download={document.originalName}
                className="mt-4 inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
              >
                Télécharger le fichier
              </a>
            </div>
          )}

          {loading && (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Composant pour afficher les détails d'une candidature
const ApplicationDetails = ({ application }) => {
  const [selectedDocument, setSelectedDocument] = useState(null);

  const formatSituation = (situation) => {
    const situations = {
      'employed': 'Employé(e)',
      'job_seeking': 'En recherche d\'emploi',
      'starting_business': 'Création d\'entreprise'
    };
    return situations[situation] || situation;
  };

  const formatProgram = (program) => {
    const programs = {
      'EMBA': 'Executive MBA',
      'Executive MBA': 'Executive MBA',
      'Part-time MBA': 'MBA à temps partiel',
      'Full-time MBA': 'MBA à temps plein'
    };
    return programs[program] || program;
  };

  const formatStudyMode = (mode) => {
    const modes = {
      'full_time': 'Temps plein',
      'part_time': 'Temps partiel',
      'weekend': 'Week-end',
      'evening': 'Soirée'
    };
    return modes[mode] || mode;
  };

  return (
    <div className="space-y-6">
      {/* Timeline */}
      <ApplicationTimeline application={application} />

      {/* Informations personnelles */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
          <UserIcon className="h-5 w-5 mr-2" />
          Informations personnelles
        </h3>
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Civilité:</span>
              <p className="text-gray-900 dark:text-white">{application.title === 'Mr' ? 'Monsieur' : 'Madame'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Nom:</span>
              <p className="text-gray-900 dark:text-white">{application.lastName}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Prénom:</span>
              <p className="text-gray-900 dark:text-white">{application.firstName}</p>
            </div>
            {application.marriedName && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Nom marital:</span>
                <p className="text-gray-900 dark:text-white">{application.marriedName}</p>
              </div>
            )}
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Email:</span>
              <p className="text-gray-900 dark:text-white">{application.email}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Téléphone:</span>
              <p className="text-gray-900 dark:text-white">{application.phone}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Mobile:</span>
              <p className="text-gray-900 dark:text-white">{application.mobilePhone}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Date de naissance:</span>
              <p className="text-gray-900 dark:text-white">
                {application.dateOfBirth ? new Date(application.dateOfBirth).toLocaleDateString('fr-FR') : 'N/A'}
              </p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Lieu de naissance:</span>
              <p className="text-gray-900 dark:text-white">{application.placeOfBirth || 'N/A'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Nationalité:</span>
              <p className="text-gray-900 dark:text-white">{application.nationality}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Adresse:</span>
              <p className="text-gray-900 dark:text-white">{application.address}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Code postal:</span>
              <p className="text-gray-900 dark:text-white">{application.postalCode}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Ville:</span>
              <p className="text-gray-900 dark:text-white">{application.city}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Pays:</span>
              <p className="text-gray-900 dark:text-white">{application.country}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Situation actuelle:</span>
              <p className="text-gray-900 dark:text-white">{formatSituation(application.currentSituation)}</p>
            </div>
            {application.employerInformed && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Employeur informé:</span>
                <p className="text-gray-900 dark:text-white">{application.employerInformed === 'yes' ? 'Oui' : 'Non'}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Informations du programme */}
      {application.programInfo && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Programme demandé
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Programme:</span>
                <p className="text-gray-900 dark:text-white">{formatProgram(application.programInfo.program)}</p>
              </div>
              {application.programInfo.specialization && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Spécialisation:</span>
                  <p className="text-gray-900 dark:text-white">{application.programInfo.specialization}</p>
                </div>
              )}
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Année d'admission:</span>
                <p className="text-gray-900 dark:text-white">{application.programInfo.intakeYear}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Semestre:</span>
                <p className="text-gray-900 dark:text-white">{application.programInfo.intakeSemester}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Mode d'étude:</span>
                <p className="text-gray-900 dark:text-white">{formatStudyMode(application.programInfo.studyMode)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Poste actuel */}
      {application.currentPosition && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <BriefcaseIcon className="h-5 w-5 mr-2" />
            Poste actuel ou dernier poste
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Poste:</span>
                <p className="text-gray-900 dark:text-white">{application.currentPosition}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Entreprise:</span>
                <p className="text-gray-900 dark:text-white">{application.companyName}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Secteur d'activité:</span>
                <p className="text-gray-900 dark:text-white">{application.activitySector}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Date de début:</span>
                <p className="text-gray-900 dark:text-white">{application.positionStartDate}</p>
              </div>
              {application.positionEndDate && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Date de fin:</span>
                  <p className="text-gray-900 dark:text-white">{application.positionEndDate}</p>
                </div>
              )}
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Nombre de subordonnés:</span>
                <p className="text-gray-900 dark:text-white">{application.numberOfSubordinates || 0}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Temps plein:</span>
                <p className="text-gray-900 dark:text-white">{application.fullTime ? 'Oui' : 'Non'}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Adresse entreprise:</span>
                <p className="text-gray-900 dark:text-white">{application.companyAddress}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Téléphone entreprise:</span>
                <p className="text-gray-900 dark:text-white">{application.companyPhone}</p>
              </div>
            </div>
            {application.positionDescription && (
              <div className="mt-4">
                <span className="font-medium text-gray-700 dark:text-gray-300">Description du poste:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.positionDescription}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Formation académique */}
      {application.academicBackground && application.academicBackground.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Formation académique
          </h3>
          <div className="space-y-4">
            {application.academicBackground.map((edu, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Diplôme:</span>
                    <p className="text-gray-900 dark:text-white">{edu.degree}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Institution:</span>
                    <p className="text-gray-900 dark:text-white">{edu.institution}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Année:</span>
                    <p className="text-gray-900 dark:text-white">{edu.year}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Pays:</span>
                    <p className="text-gray-900 dark:text-white">{edu.country}</p>
                  </div>
                  {edu.fieldOfStudy && (
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Domaine d'étude:</span>
                      <p className="text-gray-900 dark:text-white">{edu.fieldOfStudy}</p>
                    </div>
                  )}
                  {edu.gpa && (
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Note/GPA:</span>
                      <p className="text-gray-900 dark:text-white">{edu.gpa}</p>
                    </div>
                  )}
                </div>
                {edu.achievements && edu.achievements.length > 0 && (
                  <div className="mt-4">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Réalisations:</span>
                    <ul className="text-gray-900 dark:text-white mt-2 text-sm space-y-1">
                      {edu.achievements.map((achievement, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-gray-400 mr-2">•</span>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Expérience professionnelle */}
      {application.professionalExperience && application.professionalExperience.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <BriefcaseIcon className="h-5 w-5 mr-2" />
            Expérience professionnelle
          </h3>
          <div className="space-y-4">
            {application.professionalExperience.map((work, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Poste:</span>
                    <p className="text-gray-900 dark:text-white">{work.position}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Entreprise:</span>
                    <p className="text-gray-900 dark:text-white">{work.company}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Période:</span>
                    <p className="text-gray-900 dark:text-white">{work.startDate} - {work.endDate || 'Présent'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Secteur:</span>
                    <p className="text-gray-900 dark:text-white">{work.sector}</p>
                  </div>
                  {work.location && (
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Lieu:</span>
                      <p className="text-gray-900 dark:text-white">{work.location}</p>
                    </div>
                  )}
                  {work.employmentType && (
                    <div>
                      <span className="font-medium text-gray-700 dark:text-gray-300">Type d'emploi:</span>
                      <p className="text-gray-900 dark:text-white">{work.employmentType}</p>
                    </div>
                  )}
                </div>
                {work.responsibilities && (
                  <div className="mt-4">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Responsabilités:</span>
                    <p className="text-gray-900 dark:text-white text-sm mt-2 leading-relaxed">{work.responsibilities}</p>
                  </div>
                )}
                {work.achievements && work.achievements.length > 0 && (
                  <div className="mt-4">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Réalisations:</span>
                    <ul className="text-gray-900 dark:text-white mt-2 text-sm space-y-1">
                      {work.achievements.map((achievement, idx) => (
                        <li key={idx} className="flex items-start">
                          <span className="text-gray-400 mr-2">•</span>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Langues */}
      {application.languages && application.languages.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <DocumentTextIcon className="h-5 w-5 mr-2" />
            Langues
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {application.languages.map((lang, index) => (
                <div key={index} className="text-sm">
                  <div className="font-medium text-gray-900 dark:text-white">{lang.language}</div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Oral: {lang.oralLevel} • Écrit: {lang.writtenLevel}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Attentes de formation */}
      {application.trainingExpectations && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <AcademicCapIcon className="h-5 w-5 mr-2" />
            Attentes de formation
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <p className="text-gray-900 dark:text-white text-sm leading-relaxed">
              {application.trainingExpectations}
            </p>
          </div>
        </div>
      )}

      {/* Motivation et projet professionnel */}
      {application.professionalProject && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <BriefcaseIcon className="h-5 w-5 mr-2" />
            Projet professionnel
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-4">
            {application.professionalProject && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Description du projet:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.professionalProject}
                </p>
              </div>
            )}
            {application.professionalProject.timeline && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Échéancier:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.professionalProject.timeline}
                </p>
              </div>
            )}
            {application.professionalProject.expectedOutcome && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Résultats attendus:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.professionalProject.expectedOutcome}
                </p>
              </div>
            )}
            {application.professionalProject.requiredResources && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Ressources requises:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.professionalProject.requiredResources}
                </p>
              </div>
            )}
            {application.professionalProject.successMetrics && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Métriques de succès:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.professionalProject.successMetrics}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Activités et centres d'intérêt */}
      {application.activities && application.activities.length > 0 && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <UserIcon className="h-5 w-5 mr-2" />
            Activités et centres d'intérêt
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="space-y-3">
              {application.activities.map((activity, index) => (
                <div key={index} className="text-sm">
                  <div className="font-medium text-gray-900 dark:text-white">{activity.type}</div>
                  <div className="text-gray-600 dark:text-gray-400 mt-1">{activity.description}</div>
                  {activity.duration && (
                    <div className="text-gray-500 dark:text-gray-500 text-xs mt-1">
                      Durée: {activity.duration}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Documents */}
      {(() => {
        // Transformer l'objet documents en tableau pour l'affichage
        const documentsList = [];
        if (application.documents) {
          const docTypes = [
            { key: 'cv', label: 'CV', required: true },
            { key: 'idCard', label: 'Carte d\'identité', required: true },
            { key: 'photo', label: 'Photo', required: true },
            { key: 'diplomas', label: 'Diplômes', required: true },
            { key: 'recommendationLetters', label: 'Lettres de recommandation', required: true },
            { key: 'experienceCertificate', label: 'Certificat d\'expérience', required: false }
          ];

          docTypes.forEach(docType => {
            const doc = application.documents[docType.key];
            if (doc && doc.filename) {
              documentsList.push({
                ...doc,
                type: docType.label,
                required: docType.required,
                key: docType.key
              });
            }
          });
        }

        return documentsList.length > 0 && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              Documents soumis ({documentsList.length})
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {documentsList.map((doc, index) => (
                <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                     onClick={() => setSelectedDocument(doc)}>
                  <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {doc.originalName || doc.filename}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {doc.type} • {doc.size ? (doc.size / 1024 / 1024).toFixed(2) + ' MB' : 'Taille inconnue'}
                      {doc.required && <span className="ml-1 text-red-500">*</span>}
                    </p>
                  </div>
                  <EyeIcon className="h-4 w-4 text-gray-400 ml-2" />
                </div>
              ))}
            </div>
          </div>
        );
      })()}

      {/* Informations d'entretien */}
      {application.interview && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <CalendarDaysIcon className="h-5 w-5 mr-2" />
            Entretien
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              {application.interview.scheduledDate && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Date programmée:</span>
                  <p className="text-gray-900 dark:text-white">
                    {new Date(application.interview.scheduledDate).toLocaleDateString('fr-FR', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              )}
              {application.interview.duration && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Durée:</span>
                  <p className="text-gray-900 dark:text-white">{application.interview.duration} minutes</p>
                </div>
              )}
              {application.interview.location && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Lieu:</span>
                  <p className="text-gray-900 dark:text-white">{application.interview.location}</p>
                </div>
              )}
              {application.interview.type && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                  <p className="text-gray-900 dark:text-white">
                    {application.interview.type === 'in_person' ? 'En personne' :
                     application.interview.type === 'video' ? 'Visioconférence' :
                     application.interview.type === 'phone' ? 'Téléphone' :
                     application.interview.type}
                  </p>
                </div>
              )}
              {application.interview.status && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Statut:</span>
                  <p className={cn(
                    "text-sm font-medium",
                    application.interview.status === 'completed' ? 'text-green-600 dark:text-green-400' :
                    application.interview.status === 'scheduled' ? 'text-blue-600 dark:text-blue-400' :
                    application.interview.status === 'cancelled' ? 'text-red-600 dark:text-red-400' :
                    'text-gray-600 dark:text-gray-400'
                  )}>
                    {application.interview.status === 'completed' ? 'Terminé' :
                     application.interview.status === 'scheduled' ? 'Programmé' :
                     application.interview.status === 'cancelled' ? 'Annulé' :
                     application.interview.status}
                  </p>
                </div>
              )}
            </div>

            {/* Interviewers */}
            {application.interview.interviewers && application.interview.interviewers.length > 0 && (
              <div className="mt-4">
                <span className="font-medium text-gray-700 dark:text-gray-300">Interviewers:</span>
                <div className="mt-2 space-y-1">
                  {application.interview.interviewers.map((interviewer, index) => (
                    <div key={index} className="text-sm text-gray-900 dark:text-white">
                      {interviewer.interviewer?.firstName} {interviewer.interviewer?.lastName}
                      {interviewer.role && <span className="text-gray-500 dark:text-gray-400 ml-2">({interviewer.role})</span>}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Notes d'entretien */}
            {application.interview.notes && (
              <div className="mt-4">
                <span className="font-medium text-gray-700 dark:text-gray-300">Notes:</span>
                <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                  {application.interview.notes}
                </p>
              </div>
            )}

            {/* Évaluation */}
            {application.interview.evaluation && application.interview.evaluation.completed && (
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Évaluation</h4>

                {/* Scores */}
                {application.interview.evaluation.scores && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    {Object.entries(application.interview.evaluation.scores).map(([key, value]) => (
                      <div key={key} className="text-sm">
                        <span className="font-medium text-gray-700 dark:text-gray-300">
                          {key === 'communication' ? 'Communication' :
                           key === 'leadership' ? 'Leadership' :
                           key === 'motivation' ? 'Motivation' :
                           key === 'experience' ? 'Expérience' :
                           key === 'fitForProgram' ? 'Adéquation programme' :
                           key === 'overallScore' ? 'Score global' : key}:
                        </span>
                        <p className="text-gray-900 dark:text-white font-medium">{value}/10</p>
                      </div>
                    ))}
                  </div>
                )}

                {/* Recommandation */}
                {application.interview.evaluation.recommendation && (
                  <div className="mb-4">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Recommandation:</span>
                    <p className={cn(
                      "text-sm font-medium mt-1",
                      application.interview.evaluation.recommendation === 'strongly_recommend' ? 'text-green-600 dark:text-green-400' :
                      application.interview.evaluation.recommendation === 'recommend' ? 'text-green-500 dark:text-green-300' :
                      application.interview.evaluation.recommendation === 'neutral' ? 'text-yellow-600 dark:text-yellow-400' :
                      application.interview.evaluation.recommendation === 'not_recommend' ? 'text-red-500 dark:text-red-300' :
                      application.interview.evaluation.recommendation === 'strongly_not_recommend' ? 'text-red-600 dark:text-red-400' :
                      'text-gray-600 dark:text-gray-400'
                    )}>
                      {application.interview.evaluation.recommendation === 'strongly_recommend' ? 'Fortement recommandé' :
                       application.interview.evaluation.recommendation === 'recommend' ? 'Recommandé' :
                       application.interview.evaluation.recommendation === 'neutral' ? 'Neutre' :
                       application.interview.evaluation.recommendation === 'not_recommend' ? 'Non recommandé' :
                       application.interview.evaluation.recommendation === 'strongly_not_recommend' ? 'Fortement non recommandé' :
                       application.interview.evaluation.recommendation}
                    </p>
                  </div>
                )}

                {/* Points forts */}
                {application.interview.evaluation.strengths && application.interview.evaluation.strengths.length > 0 && (
                  <div className="mb-4">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Points forts:</span>
                    <ul className="text-gray-900 dark:text-white mt-2 text-sm space-y-1">
                      {application.interview.evaluation.strengths.map((strength, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-green-400 mr-2">•</span>
                          {strength}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Préoccupations */}
                {application.interview.evaluation.concerns && application.interview.evaluation.concerns.length > 0 && (
                  <div className="mb-4">
                    <span className="font-medium text-gray-700 dark:text-gray-300">Préoccupations:</span>
                    <ul className="text-gray-900 dark:text-white mt-2 text-sm space-y-1">
                      {application.interview.evaluation.concerns.map((concern, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-red-400 mr-2">•</span>
                          {concern}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Commentaires */}
                {application.interview.evaluation.comments && (
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">Commentaires:</span>
                    <p className="text-gray-900 dark:text-white mt-2 text-sm leading-relaxed">
                      {application.interview.evaluation.comments}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Signature numérique */}
      {application.signature && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <CheckIcon className="h-5 w-5 mr-2" />
            Signature numérique
          </h3>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Statut:</span>
                <p className={cn(
                  "text-sm font-medium",
                  application.signature.status === 'signed' ? 'text-green-600 dark:text-green-400' :
                  application.signature.status === 'pending' ? 'text-yellow-600 dark:text-yellow-400' :
                  application.signature.status === 'declined' ? 'text-red-600 dark:text-red-400' :
                  'text-gray-600 dark:text-gray-400'
                )}>
                  {application.signature.status === 'signed' ? 'Signé' :
                   application.signature.status === 'pending' ? 'En attente' :
                   application.signature.status === 'declined' ? 'Refusé' :
                   application.signature.status}
                </p>
              </div>
              {application.signature.provider && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Fournisseur:</span>
                  <p className="text-gray-900 dark:text-white">{application.signature.provider}</p>
                </div>
              )}
              {application.signature.timestamp && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Horodatage:</span>
                  <p className="text-gray-900 dark:text-white">
                    {new Date(application.signature.timestamp).toLocaleDateString('fr-FR', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              )}
              {application.signature.ipAddress && (
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">Adresse IP:</span>
                  <p className="text-gray-900 dark:text-white">{application.signature.ipAddress}</p>
                </div>
              )}
              {application.signature.userAgent && (
                <div className="md:col-span-2">
                  <span className="font-medium text-gray-700 dark:text-gray-300">Navigateur:</span>
                  <p className="text-gray-900 dark:text-white text-xs">{application.signature.userAgent}</p>
                </div>
              )}
            </div>

            {/* Affichage de la signature manuscrite */}
            {application.signature.data && (
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <span className="font-medium text-gray-700 dark:text-gray-300">Signature manuscrite:</span>
                <div className="mt-2 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                  <img
                    src={application.signature.data}
                    alt="Signature manuscrite"
                    className="max-w-full h-auto max-h-32 mx-auto"
                    style={{ filter: 'invert(0)' }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Visualiseur de document */}
      {selectedDocument && (
        <DocumentViewer
          document={selectedDocument}
          onClose={() => setSelectedDocument(null)}
        />
      )}
    </div>
  );
};

export default Applications;
