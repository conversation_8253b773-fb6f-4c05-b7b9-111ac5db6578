const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class DocuSealService {
  constructor() {
    this.BASE_URL = process.env.DOCUSEAL_API_URL || 'https://api.docuseal.co';
    this.API_KEY = process.env.DOCUSEAL_API_KEY || 'YOUR_DOCUSEAL_API_KEY';
  }

  async request(endpoint = '', options = {}, headers = {}) {
    const url = `${this.BASE_URL}/${endpoint}`;
    const config = {
      url,
      headers: {
        'X-Auth-Token': this.API_KEY,
        'Content-Type': 'application/json',
        ...headers
      },
      ...options
    };

    try {
      const res = await axios(config);
      return res.data;
    } catch (error) {
      console.error('❌ Erreur API DocuSeal:', error.response?.data || error.message);
      throw new Error(`Erreur API DocuSeal: ${error.response?.data?.message || error.message}`);
    }
  }

  // Créer un template de signature pour une candidature EMBA
  async createSignatureTemplate(applicationData) {
    const body = {
      name: `Template Candidature EMBA - ${applicationData.nom} ${applicationData.prenom}`,
      fields: [
        {
          name: 'signature',
          type: 'signature',
          required: true,
          page: 0,
          x: 400,
          y: 650,
          w: 150,
          h: 50
        },
        {
          name: 'date',
          type: 'date',
          required: true,
          page: 0,
          x: 400,
          y: 600,
          w: 100,
          h: 20
        }
      ]
    };

    const options = {
      method: 'POST',
      data: JSON.stringify(body)
    };

    return this.request('templates', options);
  }

  // Créer une soumission de signature avec document
  async createSubmissionWithDocument(templateId, applicationData, documentPath) {
    const form = new FormData();

    // Ajouter le document
    form.append('files[]', fs.createReadStream(documentPath), {
      filename: `candidature_${applicationData.nom}_${applicationData.prenom}.pdf`
    });

    // Ajouter les données de soumission
    const submissionData = {
      template_id: templateId,
      send_email: true,
      submitters: [
        {
          name: `${applicationData.prenom} ${applicationData.nom}`,
          email: applicationData.email,
          role: 'Candidat'
        }
      ]
    };

    form.append('submission', JSON.stringify(submissionData));

    const options = {
      method: 'POST',
      data: form
    };

    const headers = {
      ...form.getHeaders(),
      'X-Auth-Token': this.API_KEY
    };
    delete headers['Content-Type']; // Let form-data set the content type

    return this.request('submissions', options, headers);
  }

  // Obtenir le statut d'une soumission
  async getSubmissionStatus(submissionId) {
    const options = {
      method: 'GET'
    };
    return this.request(`submissions/${submissionId}`, options);
  }

  // Télécharger le document signé
  async downloadSignedDocument(submissionId) {
    const options = {
      method: 'GET',
      responseType: 'stream'
    };
    return this.request(`submissions/${submissionId}/download`, options);
  }

  // Processus complet de signature pour une candidature EMBA
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🔏 Début du processus de signature DocuSeal pour:', applicationData.email);

      // 1. Créer un template de signature
      const template = await this.createSignatureTemplate(applicationData);
      console.log('✅ Template créé:', template.id);

      // 2. Créer une soumission avec le document
      const submission = await this.createSubmissionWithDocument(
        template.id,
        applicationData,
        documentPath
      );
      console.log('✅ Soumission créée:', submission.id);

      return {
        success: true,
        submissionId: submission.id,
        templateId: template.id,
        message: 'Demande de signature envoyée avec succès'
      };

    } catch (error) {
      console.error('❌ Erreur lors du processus de signature:', error);
      return {
        success: false,
        error: error.message,
        message: 'Erreur lors de l\'envoi de la demande de signature'
      };
    }
  }
}

module.exports = new DocuSealService();
