const express = require('express');
const router = express.Router();
const Notification = require('../models/Notification');
const User = require('../models/User');
const { authenticate, authorize } = require('../middleware/auth');

// GET /api/notifications - Obtenir toutes les notifications pour l'admin
router.get('/', authenticate, async (req, res) => {
  try {
    console.log('🔔 Récupération des notifications avec filtres:', req.query);

    const { page = 1, limit = 50, recipient, type, status, search } = req.query;
    const query = {};

    // Appliquer les filtres
    if (recipient) query.recipient = recipient;
    if (type) query.type = type;
    if (status) query.status = status;

    // Recherche textuelle
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } }
      ];
    }

    const notifications = await Notification.find(query)
      .populate('recipient', 'firstName lastName email')
      .populate('sender', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Notification.countDocuments(query);

    // Enrichir les données pour l'interface admin
    const enrichedNotifications = notifications.map(notification => ({
      id: notification._id,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      priority: notification.priority || 'medium',
      status: notification.status,
      recipients: {
        type: notification.recipientType || 'specific',
        count: notification.recipientCount || 1,
        specific: notification.recipient ? [notification.recipient._id] : []
      },
      createdAt: notification.createdAt,
      sentAt: notification.sentAt,
      readCount: notification.readCount || 0,
      createdBy: notification.sender ?
        `${notification.sender.firstName} ${notification.sender.lastName}` :
        'Admin'
    }));

    console.log(`✅ ${enrichedNotifications.length} notifications récupérées`);

    res.json({
      success: true,
      notifications: enrichedNotifications,
      totalPages: Math.ceil(total / limit),
      currentPage: parseInt(page),
      total
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notifications',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id)
      .populate('recipient', 'firstName lastName email')
      .populate('sender', 'firstName lastName');
    
    if (!notification) return res.status(404).json({ message: 'Notification not found' });
    res.json(notification);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.post('/', async (req, res) => {
  try {
    const notification = new Notification(req.body);
    await notification.save();
    res.status(201).json(notification);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const notification = await Notification.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!notification) return res.status(404).json({ message: 'Notification not found' });
    res.json(notification);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/mark-read', async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);
    if (!notification) return res.status(404).json({ message: 'Notification not found' });
    
    await notification.markAsRead();
    res.json({ message: 'Notification marked as read' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/user/:userId/unread', async (req, res) => {
  try {
    const { userId } = req.params;
    const { limit = 50 } = req.query;
    
    const unreadNotifications = await Notification.getUnreadForUser(userId, parseInt(limit));
    res.json(unreadNotifications);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
