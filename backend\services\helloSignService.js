const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

class HelloSignService {
  constructor() {
    this.API_KEY = process.env.HELLOSIGN_API_KEY;
    this.CLIENT_ID = process.env.HELLOSIGN_CLIENT_ID;
    this.BASE_URL = 'https://api.hellosign.com/v3';
    this.WEBHOOK_URL = process.env.HELLOSIGN_WEBHOOK_URL || 'http://localhost:5000/api/applications/hellosign-webhook';
  }

  // Configuration des headers d'authentification
  getAuthHeaders() {
    return {
      'Authorization': `Basic ${Buffer.from(this.API_KEY + ':').toString('base64')}`,
      'Content-Type': 'application/json'
    };
  }

  // Créer une demande de signature
  async createSignatureRequest(applicationData, documentPath) {
    try {
      console.log('📝 Création demande de signature HelloSign pour:', applicationData.email);

      const form = new FormData();
      
      // Configuration de la demande
      form.append('title', `Candidature EMBA - ${applicationData.prenom} ${applicationData.nom}`);
      form.append('subject', 'Signature requise pour votre candidature EMBA');
      form.append('message', `Bonjour ${applicationData.prenom},\n\nVeuillez signer ce document pour finaliser votre candidature au programme EMBA.\n\nCordialement,\nÉquipe EMBA`);
      
      // Signataire
      form.append('signers[0][email_address]', applicationData.email);
      form.append('signers[0][name]', `${applicationData.prenom} ${applicationData.nom}`);
      form.append('signers[0][order]', '0');
      
      // Configuration
      form.append('test_mode', process.env.NODE_ENV !== 'production' ? '1' : '0');
      form.append('use_text_tags', '0');
      form.append('hide_text_tags', '1');
      
      // Webhook pour notifications
      if (this.WEBHOOK_URL) {
        form.append('signing_redirect_url', `${process.env.BASE_URL || 'http://localhost:5000'}/signature-complete`);
      }

      // Ajouter le document
      if (fs.existsSync(documentPath)) {
        form.append('file[0]', fs.createReadStream(documentPath));
      } else {
        // Créer un document temporaire avec les infos de candidature
        const tempContent = this.generateApplicationDocument(applicationData);
        const tempPath = documentPath.replace('.pdf', '.txt');
        fs.writeFileSync(tempPath, tempContent);
        form.append('file[0]', fs.createReadStream(tempPath));
      }

      // Métadonnées personnalisées
      form.append('metadata[application_id]', applicationData.applicationId);
      form.append('metadata[application_number]', applicationData.applicationNumber || 'N/A');

      const response = await axios.post(`${this.BASE_URL}/signature_request/send`, form, {
        headers: {
          'Authorization': `Basic ${Buffer.from(this.API_KEY + ':').toString('base64')}`,
          ...form.getHeaders()
        }
      });

      console.log('✅ Demande HelloSign créée:', response.data.signature_request.signature_request_id);

      return {
        success: true,
        signatureRequestId: response.data.signature_request.signature_request_id,
        signUrl: response.data.signature_request.signing_url,
        message: 'Demande de signature envoyée avec succès',
        provider: 'hellosign'
      };

    } catch (error) {
      console.error('❌ Erreur HelloSign:', error.response?.data || error.message);
      throw new Error(`Erreur HelloSign: ${error.response?.data?.error?.error_msg || error.message}`);
    }
  }

  // Obtenir le statut d'une demande de signature
  async getSignatureRequestStatus(signatureRequestId) {
    try {
      const response = await axios.get(`${this.BASE_URL}/signature_request/${signatureRequestId}`, {
        headers: this.getAuthHeaders()
      });

      return response.data.signature_request;
    } catch (error) {
      console.error('❌ Erreur statut HelloSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Télécharger le document signé
  async downloadSignedDocument(signatureRequestId) {
    try {
      const response = await axios.get(`${this.BASE_URL}/signature_request/files/${signatureRequestId}`, {
        headers: this.getAuthHeaders(),
        responseType: 'stream'
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur téléchargement HelloSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Annuler une demande de signature
  async cancelSignatureRequest(signatureRequestId) {
    try {
      const response = await axios.post(`${this.BASE_URL}/signature_request/cancel/${signatureRequestId}`, {}, {
        headers: this.getAuthHeaders()
      });

      return response.data;
    } catch (error) {
      console.error('❌ Erreur annulation HelloSign:', error.response?.data || error.message);
      throw error;
    }
  }

  // Processus complet de signature pour une candidature EMBA
  async processApplicationSignature(applicationData, documentPath) {
    try {
      console.log('🔏 Début du processus HelloSign pour:', applicationData.email);

      // Vérifier la configuration
      if (!this.API_KEY) {
        throw new Error('HELLOSIGN_API_KEY non configurée');
      }

      // Créer la demande de signature
      const result = await this.createSignatureRequest(applicationData, documentPath);

      console.log('✅ Processus HelloSign initié:', result.signatureRequestId);

      return result;

    } catch (error) {
      console.error('❌ Erreur processus HelloSign:', error);
      return {
        success: false,
        error: error.message,
        provider: 'hellosign'
      };
    }
  }

  // Générer le contenu du document de candidature
  generateApplicationDocument(applicationData) {
    return `
CANDIDATURE PROGRAMME EMBA
==========================

Informations du candidat:
- Nom: ${applicationData.nom}
- Prénom: ${applicationData.prenom}
- Email: ${applicationData.email}
- Téléphone: ${applicationData.telephone}

Numéro de candidature: ${applicationData.applicationNumber || 'En cours d\'attribution'}
Date de soumission: ${new Date().toLocaleDateString('fr-FR')}

DÉCLARATION ET SIGNATURE
========================

Je, soussigné(e) ${applicationData.prenom} ${applicationData.nom}, déclare que:

1. Toutes les informations fournies dans cette candidature sont exactes et complètes
2. Je m'engage à respecter le règlement du programme EMBA
3. J'autorise le traitement de mes données personnelles dans le cadre de cette candidature
4. Je comprends que toute fausse déclaration peut entraîner le rejet de ma candidature

Cette signature électronique a la même valeur légale qu'une signature manuscrite.

Date: ${new Date().toLocaleDateString('fr-FR')}
Lieu: [À compléter par le candidat]

Signature électronique: [Zone de signature HelloSign]
`;
  }

  // Valider un webhook HelloSign
  validateWebhook(eventHash, eventTime, eventType) {
    // HelloSign utilise un hash pour valider les webhooks
    // Implémentation simplifiée - en production, utilisez la clé secrète
    return true;
  }
}

module.exports = new HelloSignService();
