const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
  // Numéro de candidature unique
  applicationNumber: {
    type: String,
    unique: true,
    trim: true
  },
  
  // PERSONAL INFORMATION (Step 1)
  title: {
    type: String,
    enum: ['Mrs', 'Mr'],
    required: true
  },
  lastName: { type: String, required: true, trim: true, maxlength: 50 },
  marriedName: { type: String, trim: true, maxlength: 50 },
  firstName: { type: String, required: true, trim: true, maxlength: 50 },

  currentSituation: {
    type: String,
    enum: ['employed', 'job_seeking', 'starting_business'],
    required: true
  },
  employerInformed: {
    type: String,
    enum: ['yes', 'no']
  },

  // Required photo
  photo: {
    filename: { type: String, required: true },
    path: { type: String, required: true },
    uploadDate: { type: Date, default: Date.now }
  },

  // Personal information
  dateOfBirth: { type: Date, required: true },
  cityOfBirth: { type: String, required: true, trim: true },
  countryOfBirth: { type: String, required: true, trim: true },
  nationality: { type: String, required: true, trim: true },

  // Contact
  phone: {
    type: String,
    required: true,
    trim: true
  },
  mobilePhone: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  
  // Programme demandé
  programInfo: {
    program: {
      type: String,
      enum: ['EMBA', 'Executive MBA', 'Part-time MBA', 'Full-time MBA'],
      required: true,
      default: 'EMBA'
    },
    specialization: {
      type: String,
      enum: ['General Management', 'Finance', 'Marketing', 'Operations', 'Strategy', 'Digital Transformation', 'Entrepreneurship']
    },
    intakeYear: {
      type: String,
      required: true,
      match: [/^\d{4}$/, 'Format: YYYY']
    },
    intakeSemester: {
      type: String,
      enum: ['Fall', 'Spring', 'Summer'],
      required: true
    },
    studyMode: {
      type: String,
      enum: ['full_time', 'part_time', 'weekend', 'evening'],
      default: 'part_time'
    }
  },
  
  // ACADEMIC BACKGROUND (Step 2) - Dynamic array
  academicBackground: [{
    year: { type: String, required: true, trim: true },
    degree: { type: String, required: true, trim: true },
    institution: { type: String, required: true, trim: true },
    country: { type: String, required: true, trim: true }
  }],
  
  // PROFESSIONAL EXPERIENCE (Step 3)
  yearsOfExperience: { type: Number, required: true, min: 0 },
  yearsOfResponsibility: { type: Number, required: true, min: 0 },

  // CURRENT OR LAST POSITION
  currentPosition: { type: String, required: true, trim: true },
  positionStartDate: { type: String, required: true, trim: true }, // MM/YYYY
  positionEndDate: { type: String, trim: true }, // MM/YYYY or empty if current
  numberOfSubordinates: { type: Number, min: 0, default: 0 },
  fullTime: { type: Boolean, default: true },
  companyName: { type: String, required: true, trim: true },
  activitySector: { type: String, required: true, trim: true },
  companyAddress: { type: String, required: true, trim: true },
  companyPhone: { type: String, required: true, trim: true },
  positionDescription: { type: String, required: true, trim: true, maxlength: 1000 },

  // PREVIOUS EXPERIENCES (dynamic array)
  previousExperiences: [{
    position: { type: String, trim: true },
    startDate: { type: String, trim: true },
    endDate: { type: String, trim: true },
    numberOfSubordinates: { type: Number, min: 0, default: 0 },
    fullTime: { type: Boolean, default: true },
    companyName: { type: String, trim: true },
    activitySector: { type: String, trim: true },
    address: { type: String, trim: true },
    phone: { type: String, trim: true },
    description: { type: String, trim: true, maxlength: 1000 }
  }],

  situationSatisfactory: {
    type: String,
    enum: ['yes', 'no']
  },
  situationDetails: { type: String, trim: true, maxlength: 500 },
  
  // Expérience de leadership et réalisations
  leadership: {
    totalYearsExperience: { type: Number, min: 0 },
    managementExperience: { type: Number, min: 0 },
    leadershipRoles: [{
      role: { type: String, required: true, trim: true },
      organization: { type: String, required: true, trim: true },
      duration: { type: String, trim: true },
      description: { type: String, trim: true, maxlength: 500 }
    }],
    majorAchievements: [{ type: String, trim: true, maxlength: 500 }],
    awards: [{
      title: { type: String, required: true, trim: true },
      organization: { type: String, required: true, trim: true },
      year: { type: Number, min: 1950 },
      description: { type: String, trim: true }
    }]
  },
  
  // OTHER ACTIVITIES & PROFESSIONAL PROJECT (Step 5)
  // Other activities
  livedAbroad: {
    type: String,
    enum: ['yes', 'no']
  },
  abroadDetails: { type: String, trim: true, maxlength: 500 },
  passionsInterests: { type: String, trim: true, maxlength: 500 },

  // Professional project
  professionalProject: { type: String, required: true, trim: true, maxlength: 2000 },
  trainingExpectations: { type: String, required: true, trim: true, maxlength: 1500 },

  // Application
  appliedOtherPrograms: {
    type: String,
    enum: ['yes', 'no']
  },
  otherPrograms: { type: String, trim: true, maxlength: 500 },
  funding: {
    type: String,
    enum: ['personal', 'employer_full', 'employer_partial', 'other'],
    required: true
  },
  otherFunding: { type: String, trim: true },
  howDidYouKnow: {
    type: String,
    enum: ['website', 'advertising', 'press', 'company', 'friend', 'internet_search', 'info_session', 'alumni', 'other']
  },
  howDidYouKnowOther: { type: String, trim: true },
  
  // LANGUAGES (Step 4) - Language proficiency levels
  languages: {
    french: {
      spoken: { type: String, enum: ['fluent', 'good', 'average', 'beginner'], default: 'fluent' },
      read: { type: String, enum: ['fluent', 'good', 'average', 'beginner'], default: 'fluent' },
      written: { type: String, enum: ['fluent', 'good', 'average', 'beginner'], default: 'fluent' },
      test: { type: String, trim: true }
    },
    english: {
      spoken: { type: String, enum: ['fluent', 'good', 'average', 'beginner'], default: 'average' },
      read: { type: String, enum: ['fluent', 'good', 'average', 'beginner'], default: 'average' },
      written: { type: String, enum: ['fluent', 'good', 'average', 'beginner'], default: 'average' },
      test: { type: String, trim: true }
    },
    otherLanguage: {
      name: { type: String, trim: true },
      spoken: { type: String, enum: ['fluent', 'good', 'average', 'beginner'] },
      read: { type: String, enum: ['fluent', 'good', 'average', 'beginner'] },
      written: { type: String, enum: ['fluent', 'good', 'average', 'beginner'] },
      test: { type: String, trim: true }
    }
  },
  foreignLanguageUsage: {
    type: String,
    enum: ['yes', 'no']
  },
  languageUsageDetails: { type: String, trim: true, maxlength: 500 },
  
  // Tests standardisés
  standardizedTests: [{
    testType: {
      type: String,
      enum: ['GMAT', 'GRE', 'TOEFL', 'IELTS', 'Other'],
      required: true
    },
    score: { type: Number, required: true, min: 0 },
    maxScore: { type: Number, required: true, min: 0 },
    testDate: { type: Date, required: true },
    validUntil: { type: Date },
    reportSubmitted: { type: Boolean, default: false }
  }],
  
  // Recommandations
  recommendations: [{
    recommenderName: { type: String, required: true, trim: true },
    recommenderTitle: { type: String, required: true, trim: true },
    recommenderOrganization: { type: String, required: true, trim: true },
    recommenderEmail: { type: String, required: true, lowercase: true, trim: true },
    recommenderPhone: { type: String, trim: true },
    relationship: { type: String, required: true, trim: true },
    relationshipDuration: { type: String, trim: true },
    letterSubmitted: { type: Boolean, default: false },
    submissionDate: { type: Date },
    remindersSent: { type: Number, default: 0 },
    lastReminderDate: { type: Date }
  }],
  
  // DOCUMENTS (Step 6) - Required documents
  documents: {
    // Required documents
    cv: {
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    },

    // If admitted
    idCard: {
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    },
    photo: {
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    },
    diplomas: {
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    },
    recommendationLetters: {
      filename: { type: String, required: true },
      originalName: { type: String, required: true },
      path: { type: String, required: true },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    },

    // Optional documents
    experienceCertificate: {
      filename: { type: String },
      originalName: { type: String },
      path: { type: String },
      size: { type: Number },
      mimeType: { type: String },
      uploadDate: { type: Date, default: Date.now }
    }
  },
  
  // Financement
  financing: {
    fundingSource: {
      type: String,
      enum: ['self_funded', 'employer_sponsored', 'scholarship', 'loan', 'mixed'],
      required: true
    },
    employerSupport: {
      hasSupport: { type: Boolean, default: false },
      supportType: {
        type: String,
        enum: ['full_tuition', 'partial_tuition', 'time_off', 'other']
      },
      supportAmount: { type: Number, min: 0 },
      supportLetter: { type: Boolean, default: false }
    },
    scholarshipApplication: {
      applied: { type: Boolean, default: false },
      scholarshipType: { type: String, trim: true },
      amount: { type: Number, min: 0 },
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'not_applied'],
        default: 'not_applied'
      }
    }
  },
  
  // Statut de la candidature
  applicationStatus: {
    status: {
      type: String,
      enum: [
        'draft', 'submitted', 'under_review', 'interview_scheduled',
        'interview_completed', 'pending_decision', 'accepted',
        'rejected', 'waitlisted', 'deferred', 'withdrawn'
      ],
      default: 'draft'
    },
    submissionDate: { type: Date },
    reviewStartDate: { type: Date },
    decisionDate: { type: Date },
    decisionBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    decisionReason: { type: String, trim: true },
    
    // Historique des changements de statut
    statusHistory: [{
      status: {
        type: String,
        enum: [
          'draft', 'submitted', 'under_review', 'interview_scheduled',
          'interview_completed', 'pending_decision', 'accepted',
          'rejected', 'waitlisted', 'deferred', 'withdrawn'
        ],
        required: true
      },
      changedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      changeDate: { type: Date, default: Date.now },
      reason: { type: String, trim: true },
      comments: { type: String, trim: true }
    }]
  },
  
  // Processus d'entretien
  interview: {
    isRequired: { type: Boolean, default: true },
    scheduledDate: { type: Date },
    scheduledTime: { type: String, match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/ },
    duration: { type: Number, min: 15, default: 60 }, // en minutes
    format: {
      type: String,
      enum: ['in_person', 'video_call', 'phone_call'],
      default: 'video_call'
    },
    location: { type: String, trim: true },
    meetingUrl: { type: String, trim: true },
    
    // Panel d'entretien
    interviewers: [{
      interviewer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      role: { type: String, trim: true },
      isLead: { type: Boolean, default: false }
    }],
    
    // Évaluation
    evaluation: {
      completed: { type: Boolean, default: false },
      completedDate: { type: Date },
      scores: {
        communication: { type: Number, min: 1, max: 10 },
        leadership: { type: Number, min: 1, max: 10 },
        motivation: { type: Number, min: 1, max: 10 },
        experience: { type: Number, min: 1, max: 10 },
        fitForProgram: { type: Number, min: 1, max: 10 },
        overallScore: { type: Number, min: 1, max: 10 }
      },
      strengths: [{ type: String, trim: true }],
      concerns: [{ type: String, trim: true }],
      recommendation: {
        type: String,
        enum: ['strongly_recommend', 'recommend', 'neutral', 'not_recommend', 'strongly_not_recommend']
      },
      comments: { type: String, trim: true },
      evaluatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  
  // Évaluation globale
  evaluation: {
    academicScore: { type: Number, min: 0, max: 100 },
    experienceScore: { type: Number, min: 0, max: 100 },
    leadershipScore: { type: Number, min: 0, max: 100 },
    motivationScore: { type: Number, min: 0, max: 100 },
    overallScore: { type: Number, min: 0, max: 100 },
    
    evaluatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    evaluationDate: { type: Date },
    evaluationNotes: { type: String, trim: true }
  },
  
  // Communications
  communications: [{
    type: {
      type: String,
      enum: ['email', 'phone', 'meeting', 'letter', 'other']
    },
    direction: {
      type: String,
      enum: ['inbound', 'outbound']
    },
    subject: { type: String, trim: true },
    content: { type: String, trim: true },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    sentTo: { type: String, trim: true },
    sentDate: { type: Date, default: Date.now },
    isRead: { type: Boolean, default: false },
    readDate: { type: Date }
  }],
  
  // Métadonnées
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // Signature numérique (EverSign)
  signature: {
    provider: {
      type: String,
      enum: ['eversign', 'docuseal', 'yousign', 'docusign', 'adobe_sign'],
      default: 'eversign'
    },
    submissionId: { type: String }, // ID de la soumission DocuSeal
    templateId: { type: String }, // ID du template DocuSeal
    status: {
      type: String,
      enum: ['pending', 'signed', 'declined', 'expired', 'error'],
      default: 'pending'
    },
    requestedAt: { type: Date },
    signedAt: { type: Date },
    signedDocumentPath: { type: String }, // Chemin vers le document signé
    signatureMetadata: { type: mongoose.Schema.Types.Mixed } // Métadonnées additionnelles
  },

  // Entretien
  interview: {
    scheduled: { type: Boolean, default: false },
    scheduledDate: { type: Date },
    interviewId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Interview'
    },
    completed: { type: Boolean, default: false },
    completedDate: { type: Date },
    cancelled: { type: Boolean, default: false },
    cancelReason: { type: String, trim: true },
    evaluation: {
      averageScore: { type: Number, min: 1, max: 10 },
      recommendation: {
        type: String,
        enum: ['strongly_recommend', 'recommend', 'neutral', 'not_recommend', 'strongly_not_recommend']
      }
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
applicationSchema.index({ applicationNumber: 1 });
applicationSchema.index({ email: 1 });
applicationSchema.index({ 'applicationStatus.status': 1 });
applicationSchema.index({ 'programInfo.intakeYear': 1, 'programInfo.intakeSemester': 1 });
applicationSchema.index({ createdAt: -1 });

// Virtual for full name
applicationSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual to calculate age
applicationSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
});

// Virtual to calculate total experience
applicationSchema.virtual('totalExperience').get(function() {
  return this.yearsOfExperience || 0;
});

// Middleware pour générer automatiquement le numéro de candidature
applicationSchema.pre('save', async function(next) {
  if (!this.applicationNumber) {
    const year = this.programInfo.intakeYear;
    const semester = this.programInfo.intakeSemester.charAt(0); // F, S, ou Su
    const prefix = `APP${year}${semester}`;

    // Trouver le prochain numéro disponible
    let nextNumber = 1;
    let applicationNumber;
    let exists = true;

    while (exists) {
      applicationNumber = `${prefix}${String(nextNumber).padStart(4, '0')}`;
      const existingApp = await this.constructor.findOne({ applicationNumber });
      if (!existingApp) {
        exists = false;
      } else {
        nextNumber++;
      }

      // Sécurité : éviter une boucle infinie
      if (nextNumber > 9999) {
        throw new Error('Limite de numéros de candidature atteinte pour cette période');
      }
    }

    this.applicationNumber = applicationNumber;
    console.log(`📝 Numéro de candidature généré: ${this.applicationNumber}`);
  }
  next();
});

// Middleware pour enregistrer l'historique des changements de statut
applicationSchema.pre('save', function(next) {
  if (this.isModified('applicationStatus.status') && !this.isNew) {
    this.applicationStatus.statusHistory.push({
      status: this.applicationStatus.status,
      changedBy: this.updatedBy,
      changeDate: new Date()
    });
  }
  next();
});

// Méthode pour soumettre la candidature
applicationSchema.methods.submit = function(submittedBy) {
  this.applicationStatus.status = 'submitted';
  this.applicationStatus.submissionDate = new Date();
  this.updatedBy = submittedBy;
  return this.save();
};

// Méthode pour accepter la candidature
applicationSchema.methods.accept = function(acceptedBy, reason) {
  this.applicationStatus.status = 'accepted';
  this.applicationStatus.decisionDate = new Date();
  this.applicationStatus.decisionBy = acceptedBy;
  this.applicationStatus.decisionReason = reason;
  return this.save();
};

// Méthode pour rejeter la candidature
applicationSchema.methods.reject = function(rejectedBy, reason) {
  this.applicationStatus.status = 'rejected';
  this.applicationStatus.decisionDate = new Date();
  this.applicationStatus.decisionBy = rejectedBy;
  this.applicationStatus.decisionReason = reason;
  return this.save();
};

// Méthode pour programmer un entretien
applicationSchema.methods.scheduleInterview = function(date, time, interviewers, scheduledBy) {
  this.interview.scheduledDate = date;
  this.interview.scheduledTime = time;
  this.interview.interviewers = interviewers.map(interviewer => ({
    interviewer: interviewer.id,
    role: interviewer.role,
    isLead: interviewer.isLead || false
  }));
  this.applicationStatus.status = 'interview_scheduled';
  this.updatedBy = scheduledBy;
  return this.save();
};

// Method to check if application is complete
applicationSchema.methods.isComplete = function() {
  // Check required fields
  const requiredFields = [
    this.lastName, this.firstName, this.email, this.phone, this.mobilePhone,
    this.dateOfBirth, this.title, this.professionalProject, this.trainingExpectations
  ];

  const hasRequiredFields = requiredFields.every(field => field && field.toString().trim().length > 0);

  // Check required documents
  const hasRequiredDocs = this.documents.cv && this.documents.idCard &&
                         this.documents.photo && this.documents.diplomas &&
                         this.documents.recommendationLetters;

  return hasRequiredFields && hasRequiredDocs;
};

// Méthode pour calculer le score global
applicationSchema.methods.calculateOverallScore = function() {
  const scores = this.evaluation;
  if (!scores.academicScore || !scores.experienceScore || 
      !scores.leadershipScore || !scores.motivationScore) {
    return 0;
  }
  
  // Pondération : académique 25%, expérience 35%, leadership 25%, motivation 15%
  const overallScore = (scores.academicScore * 0.25) + 
                      (scores.experienceScore * 0.35) + 
                      (scores.leadershipScore * 0.25) + 
                      (scores.motivationScore * 0.15);
  
  this.evaluation.overallScore = Math.round(overallScore);
  return this.evaluation.overallScore;
};

// Méthode statique pour obtenir les statistiques des candidatures
applicationSchema.statics.getApplicationStatistics = function(filters = {}) {
  const matchConditions = {};
  
  if (filters.intakeYear) matchConditions['programInfo.intakeYear'] = filters.intakeYear;
  if (filters.program) matchConditions['programInfo.program'] = filters.program;
  if (filters.status) matchConditions['applicationStatus.status'] = filters.status;
  
  return this.aggregate([
    { $match: matchConditions },
    {
      $group: {
        _id: '$applicationStatus.status',
        count: { $sum: 1 },
        averageScore: { $avg: '$evaluation.overallScore' }
      }
    },
    {
      $group: {
        _id: null,
        totalApplications: { $sum: '$count' },
        statusBreakdown: {
          $push: {
            status: '$_id',
            count: '$count',
            averageScore: '$averageScore'
          }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Application', applicationSchema);
