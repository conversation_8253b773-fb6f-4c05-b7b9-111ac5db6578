const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Application = require('../models/Application');
const User = require('../models/User');
const Student = require('../models/Student');
const { sendStudentCredentials, sendRejectionEmail, sendInterviewEmail } = require('../services/emailService');
const youSignService = require('../services/youSignService');

// Configuration multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/applications/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Seuls les fichiers PDF, DOC, DOCX, JPG, JPEG et PNG sont autorisés'));
    }
  }
});

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, intakeYear, program } = req.query;
    const query = {};
    if (status) query['applicationStatus.status'] = status;
    if (intakeYear) query['programInfo.intakeYear'] = intakeYear;
    if (program) query['programInfo.program'] = program;

    const applications = await Application.find(query)
      .populate('assignedTo', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Application.countDocuments(query);
    res.json({ applications, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const application = await Application.findById(req.params.id)
      .populate('assignedTo', 'firstName lastName email')
      .populate('interview.interviewers.interviewer', 'firstName lastName');

    if (!application) return res.status(404).json({ message: 'Application not found' });
    res.json(application);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route publique pour soumettre une candidature avec fichiers
router.post('/submit', upload.fields([
  { name: 'cv', maxCount: 1 },
  { name: 'motivationLetter', maxCount: 1 },
  { name: 'diploma', maxCount: 1 },
  { name: 'transcripts', maxCount: 1 },
  { name: 'photo', maxCount: 1 }
]), async (req, res) => {
  try {
    console.log('📝 Nouvelle candidature reçue');
    console.log('Données:', req.body);
    console.log('Fichiers:', req.files);

    // Préparer les données de candidature
    const applicationData = {
      personalInfo: {
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        email: req.body.email,
        phone: req.body.phone,
        dateOfBirth: req.body.dateOfBirth,
        gender: req.body.gender,
        nationality: req.body.nationality,
        address: {
          street: req.body.address || '',
          city: req.body.city || '',
          zipCode: req.body.postalCode || '',
          country: req.body.country || 'Tunisia'
        }
      },

      programInfo: {
        program: 'EMBA',
        specialization: req.body.specialization || 'General Management',
        intakeYear: new Date().getFullYear().toString(),
        intakeSemester: 'Fall',
        studyMode: 'part_time'
      },

      academicBackground: [{
        degree: req.body.lastDegree || 'Bachelor',
        fieldOfStudy: 'Business', // Valeur par défaut
        institution: req.body.institution,
        country: req.body.country || 'Tunisia',
        startYear: parseInt(req.body.graduationYear) - 4 || 2020,
        endYear: parseInt(req.body.graduationYear) || 2024,
        gpa: req.body.gpa ? parseFloat(req.body.gpa) : undefined,
        isHighestDegree: true
      }],

      workExperience: [{
        company: req.body.currentCompany,
        position: req.body.currentPosition,
        industry: req.body.industry,
        startDate: new Date(Date.now() - (parseInt(req.body.yearsOfExperience?.split('-')[0]) || 5) * 365 * 24 * 60 * 60 * 1000),
        isCurrent: true,
        managementLevel: 'Mid-Level',
        teamSize: parseInt(req.body.teamSize?.split('-')[0]) || 0
      }],

      leadership: {
        totalYearsExperience: parseInt(req.body.yearsOfExperience?.split('-')[0]) || 5,
        managementExperience: parseInt(req.body.managementExperience?.split('-')[0]) || 2
      },

      motivation: {
        whyMBA: req.body.whyEMBA || req.body.motivation,
        careerGoals: req.body.careerGoals,
        whyThisSchool: req.body.motivation,
        contribution: 'À définir lors de l\'entretien'
      },

      financing: {
        fundingSource: req.body.fundingSource || 'self_funded',
        totalCost: 25000, // Coût par défaut en TND
        currency: 'TND'
      },

      applicationStatus: {
        status: 'submitted',
        submissionDate: new Date()
      },

      documents: []
    };

    // Traiter les fichiers uploadés
    if (req.files) {
      Object.keys(req.files).forEach(fieldName => {
        const file = req.files[fieldName][0];
        let documentType = 'other';

        switch (fieldName) {
          case 'cv':
            documentType = 'cv_resume';
            break;
          case 'motivationLetter':
            documentType = 'personal_statement';
            break;
          case 'diploma':
            documentType = 'diploma';
            break;
          case 'transcripts':
            documentType = 'transcript';
            break;
          case 'photo':
            documentType = 'photo';
            break;
        }

        applicationData.documents.push({
          type: documentType,
          filename: file.filename,
          originalName: file.originalname,
          path: file.path,
          size: file.size,
          mimeType: file.mimetype,
          uploadDate: new Date()
        });
      });
    }

    // Créer la candidature
    const application = new Application(applicationData);
    await application.save();

    console.log('✅ Candidature créée avec succès:', application.applicationNumber);

    // Intégration YouSign pour signature numérique
    let signatureResult = null;
    try {
      // Créer un PDF temporaire avec les données de candidature pour signature
      const candidateData = {
        applicationId: application._id,
        nom: req.body.nom || req.body.lastName,
        prenom: req.body.prenom || req.body.firstName,
        email: req.body.email,
        telephone: req.body.telephone || req.body.phone,
        telPortable: req.body.telPortable || req.body.phone
      };

      // Générer un document PDF temporaire pour la signature
      const tempDocPath = path.join(__dirname, '../temp', `candidature_${application.applicationNumber}.pdf`);

      // Créer le dossier temp s'il n'existe pas
      const tempDir = path.dirname(tempDocPath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Pour l'instant, créer un fichier temporaire simple
      // TODO: Générer un vrai PDF avec les données de candidature
      fs.writeFileSync(tempDocPath, `Candidature EMBA - ${candidateData.nom} ${candidateData.prenom}\nEmail: ${candidateData.email}\nNuméro: ${application.applicationNumber}`);

      // Lancer le processus de signature YouSign
      signatureResult = await youSignService.processApplicationSignature(candidateData, tempDocPath);

      if (signatureResult.success) {
        // Mettre à jour la candidature avec les informations de signature
        application.signature = {
          provider: 'yousign',
          signatureRequestId: signatureResult.signatureRequestId,
          documentId: signatureResult.documentId,
          signerId: signatureResult.signerId,
          status: 'pending',
          requestedAt: new Date()
        };
        await application.save();

        console.log('✅ Demande de signature YouSign envoyée:', signatureResult.signatureRequestId);
      } else {
        console.warn('⚠️ Erreur YouSign (candidature sauvegardée):', signatureResult.error);
      }

      // Nettoyer le fichier temporaire
      if (fs.existsSync(tempDocPath)) {
        fs.unlinkSync(tempDocPath);
      }

    } catch (signatureError) {
      console.error('❌ Erreur lors de l\'intégration YouSign:', signatureError);
      // Ne pas faire échouer la candidature si YouSign échoue
    }

    // Réponse de succès
    const response = {
      success: true,
      message: 'Candidature soumise avec succès !',
      applicationNumber: application.applicationNumber,
      applicationId: application._id
    };

    // Ajouter les informations de signature si disponibles
    if (signatureResult && signatureResult.success) {
      response.signature = {
        status: 'signature_requested',
        message: 'Un email de signature vous a été envoyé',
        signatureRequestId: signatureResult.signatureRequestId
      };
    }

    res.status(201).json(response);

  } catch (error) {
    console.error('❌ Erreur lors de la soumission:', error);
    res.status(400).json({
      success: false,
      message: 'Erreur lors de la soumission de la candidature',
      error: error.message
    });
  }
});

router.post('/', async (req, res) => {
  try {
    const application = new Application(req.body);
    await application.save();
    res.status(201).json(application);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const application = await Application.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!application) return res.status(404).json({ message: 'Application not found' });
    res.json(application);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/submit', async (req, res) => {
  try {
    const { submittedBy } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    if (!application.isComplete()) {
      return res.status(400).json({ message: 'Application is not complete' });
    }

    await application.submit(submittedBy);
    res.json({ message: 'Application submitted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Route pour changer le statut d'une candidature
router.patch('/:id/status', async (req, res) => {
  try {
    const { status, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    // Mettre à jour le statut
    application.applicationStatus.status = status;
    application.applicationStatus.statusHistory.push({
      status,
      changeDate: new Date(),
      reason,
      comments: reason
    });

    if (status === 'accepted') {
      application.applicationStatus.decisionDate = new Date();
      application.applicationStatus.decision = 'accepted';
    } else if (status === 'rejected') {
      application.applicationStatus.decisionDate = new Date();
      application.applicationStatus.decision = 'rejected';
      application.applicationStatus.decisionReason = reason;

      // Envoyer email de refus
      try {
        await sendRejectionEmail({
          firstName: application.personalInfo.firstName,
          lastName: application.personalInfo.lastName,
          email: application.personalInfo.email
        }, reason);
      } catch (emailError) {
        console.error('Erreur lors de l\'envoi de l\'email de refus:', emailError);
      }
    } else if (status === 'interview_scheduled') {
      // Envoyer email pour entretien
      try {
        await sendInterviewEmail({
          firstName: application.personalInfo.firstName,
          lastName: application.personalInfo.lastName,
          email: application.personalInfo.email
        });
      } catch (emailError) {
        console.error('Erreur lors de l\'envoi de l\'email d\'entretien:', emailError);
      }
    }

    await application.save();

    res.json({
      success: true,
      message: 'Statut mis à jour avec succès',
      application
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({ message: error.message });
  }
});

router.post('/:id/accept', async (req, res) => {
  try {
    const { acceptedBy, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.accept(acceptedBy, reason);
    res.json({ message: 'Application accepted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/reject', async (req, res) => {
  try {
    const { rejectedBy, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.reject(rejectedBy, reason);
    res.json({ message: 'Application rejected successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/schedule-interview', async (req, res) => {
  try {
    const { date, time, interviewers, scheduledBy } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.scheduleInterview(date, time, interviewers, scheduledBy);
    res.json({ message: 'Interview scheduled successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/statistics', async (req, res) => {
  try {
    const { intakeYear, program, status } = req.query;
    const filters = {};
    if (intakeYear) filters.intakeYear = intakeYear;
    if (program) filters.program = program;
    if (status) filters.status = status;

    const stats = await Application.getApplicationStatistics(filters);
    res.json(stats[0] || {});
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route pour créer un compte étudiant à partir d'une candidature acceptée
router.post('/:id/create-student', async (req, res) => {
  try {
    const application = await Application.findById(req.params.id);

    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    if (application.applicationStatus.status !== 'accepted') {
      return res.status(400).json({ message: 'La candidature doit être acceptée pour créer un compte étudiant' });
    }

    // Vérifier si un compte étudiant existe déjà
    const existingUser = await User.findOne({ email: application.personalInfo.email });
    if (existingUser) {
      return res.status(400).json({ message: 'Un compte existe déjà pour cet email' });
    }

    // Générer un mot de passe temporaire (utiliser la date de naissance ou un ID)
    const tempPassword = application.personalInfo.dateOfBirth ?
      application.personalInfo.dateOfBirth.toISOString().split('T')[0].replace(/-/g, '') :
      'EMBA2024';

    // Créer l'utilisateur
    const user = new User({
      firstName: application.personalInfo.firstName,
      lastName: application.personalInfo.lastName,
      email: application.personalInfo.email,
      phone: application.personalInfo.phone,
      password: tempPassword, // Sera hashé automatiquement par le middleware
      role: 'student',
      isActive: true,
      profile: {
        dateOfBirth: application.personalInfo.dateOfBirth,
        gender: application.personalInfo.gender,
        nationality: application.personalInfo.nationality,
        address: application.personalInfo.address
      }
    });

    await user.save();
    console.log('✅ Utilisateur créé avec succès:', user._id);

    // Générer un numéro d'étudiant unique
    const currentYear = new Date().getFullYear();
    const prefix = `EMBA${currentYear}`;

    // Trouver le prochain numéro disponible
    let nextNumber = 1;
    let studentNumber;
    let exists = true;

    while (exists) {
      studentNumber = `${prefix}${String(nextNumber).padStart(3, '0')}`;
      const existingStudent = await Student.findOne({ studentNumber });
      if (!existingStudent) {
        exists = false;
      } else {
        nextNumber++;
      }

      // Sécurité : éviter une boucle infinie
      if (nextNumber > 999) {
        throw new Error('Limite de numéros d\'étudiants atteinte pour cette année');
      }
    }

    console.log('📝 Numéro d\'étudiant généré:', studentNumber);

    // Créer le profil étudiant
    const student = new Student({
      user: user._id,
      studentNumber,
      academicYear: `${currentYear}-${currentYear + 1}`,
      cohort: `EMBA ${currentYear}`,
      program: application.programInfo?.program || 'EMBA',
      specialization: application.programInfo?.specialization,
      enrollmentStatus: 'active',
      enrollmentDate: new Date(),
      expectedGraduationDate: new Date(currentYear + 2, 5, 30), // 2 ans plus tard
      academicRecord: {
        currentGPA: 0,
        cumulativeGPA: 0,
        creditsCompleted: 0,
        creditsRequired: 60, // Standard EMBA program credits
        academicStanding: 'good'
      },
      tuitionFee: {
        total: application.financing?.totalCost || 25000,
        currency: application.financing?.currency || 'TND',
        paymentPlan: 'installments'
      }
    });

    await student.save();
    console.log('🎓 Étudiant créé avec succès dans la collection students:', student._id);

    // Marquer la candidature comme traitée
    application.applicationStatus.studentAccountCreated = true;
    application.applicationStatus.createdStudentId = student._id;
    await application.save();

    // Envoyer un email avec les identifiants
    try {
      await sendStudentCredentials({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        studentNumber,
        temporaryPassword: tempPassword
      });
      console.log('✅ Email d\'identifiants envoyé à:', user.email);
    } catch (emailError) {
      console.error('❌ Erreur lors de l\'envoi de l\'email:', emailError);
    }

    res.json({
      success: true,
      message: 'Compte étudiant créé avec succès et email envoyé',
      studentNumber,
      email: user.email,
      temporaryPassword: tempPassword,
      studentId: student._id
    });

  } catch (error) {
    console.error('Erreur lors de la création du compte étudiant:', error);
    res.status(500).json({ message: error.message });
  }
});

// Webhook YouSign pour recevoir les notifications de signature
router.post('/yousign-webhook', async (req, res) => {
  try {
    console.log('🔔 Webhook YouSign reçu:', req.body);

    const { event_name, data } = req.body;

    if (event_name === 'signature_request.signed') {
      // Le document a été signé
      const signatureRequestId = data.signature_request.id;

      // Trouver la candidature correspondante
      const application = await Application.findOne({
        'signature.signatureRequestId': signatureRequestId
      });

      if (application) {
        // Mettre à jour le statut de signature
        application.signature.status = 'signed';
        application.signature.signedAt = new Date();
        application.signature.signatureMetadata = data;

        // Optionnel: Télécharger le document signé
        try {
          const signedDoc = await youSignService.downloadSignedDocument(
            signatureRequestId,
            application.signature.documentId
          );

          // Sauvegarder le document signé
          const signedDocPath = path.join(__dirname, '../uploads/signed/', `${application.applicationNumber}_signed.pdf`);
          const signedDir = path.dirname(signedDocPath);
          if (!fs.existsSync(signedDir)) {
            fs.mkdirSync(signedDir, { recursive: true });
          }

          // TODO: Sauvegarder le stream du document signé
          application.signature.signedDocumentPath = signedDocPath;
        } catch (downloadError) {
          console.error('❌ Erreur téléchargement document signé:', downloadError);
        }

        await application.save();
        console.log('✅ Candidature mise à jour avec signature:', application.applicationNumber);

        // Optionnel: Envoyer une notification à l'admin
        // TODO: Implémenter notification admin
      }
    } else if (event_name === 'signature_request.declined') {
      // Le document a été refusé
      const signatureRequestId = data.signature_request.id;

      const application = await Application.findOne({
        'signature.signatureRequestId': signatureRequestId
      });

      if (application) {
        application.signature.status = 'declined';
        application.signature.signatureMetadata = data;
        await application.save();
        console.log('⚠️ Signature refusée pour:', application.applicationNumber);
      }
    }

    res.status(200).json({ success: true, message: 'Webhook traité' });

  } catch (error) {
    console.error('❌ Erreur webhook YouSign:', error);
    res.status(500).json({ success: false, message: 'Erreur webhook' });
  }
});

// Route pour servir les documents des candidatures
router.get('/documents/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, '../uploads/applications/', filename);

    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    // Obtenir les informations du fichier
    const stats = fs.statSync(filePath);
    const ext = path.extname(filename).toLowerCase();

    // Définir le type MIME
    let mimeType = 'application/octet-stream';
    switch (ext) {
      case '.pdf':
        mimeType = 'application/pdf';
        break;
      case '.jpg':
      case '.jpeg':
        mimeType = 'image/jpeg';
        break;
      case '.png':
        mimeType = 'image/png';
        break;
      case '.gif':
        mimeType = 'image/gif';
        break;
      case '.doc':
        mimeType = 'application/msword';
        break;
      case '.docx':
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
    }

    // Définir les headers
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache 1 heure

    // Envoyer le fichier
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('❌ Erreur lors de la lecture du document:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la lecture du document'
    });
  }
});

module.exports = router;
