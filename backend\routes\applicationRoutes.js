const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Application = require('../models/Application');
const User = require('../models/User');
const Student = require('../models/Student');
const { sendStudentCredentials, sendRejectionEmail, sendInterviewEmail } = require('../services/emailService');


// Configuration multer pour l'upload de fichiers
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/applications/');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Seuls les fichiers PDF, DOC, DOCX, JPG, JPEG et PNG sont autorisés'));
    }
  }
});

router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, intakeYear, program } = req.query;
    const query = {};
    if (status) query['applicationStatus.status'] = status;
    if (intakeYear) query['programInfo.intakeYear'] = intakeYear;
    if (program) query['programInfo.program'] = program;

    const applications = await Application.find(query)
      .populate('assignedTo', 'firstName lastName')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Application.countDocuments(query);
    res.json({ applications, totalPages: Math.ceil(total / limit), currentPage: page, total });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

router.get('/:id', async (req, res) => {
  try {
    const application = await Application.findById(req.params.id)
      .populate('assignedTo', 'firstName lastName email')
      .populate('interview.interviewers.interviewer', 'firstName lastName');

    if (!application) return res.status(404).json({ message: 'Application not found' });
    res.json(application);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route publique pour soumettre une candidature avec fichiers
router.post('/submit', upload.fields([
  { name: 'cv', maxCount: 1 },
  { name: 'cin', maxCount: 1 },
  { name: 'photo', maxCount: 1 },
  { name: 'diplomes', maxCount: 1 },
  { name: 'lettresRecommandation', maxCount: 1 },
  { name: 'attestationExperience', maxCount: 1 }
]), async (req, res) => {
  try {
    console.log('📝 Nouvelle candidature reçue');
    console.log('Données:', req.body);
    console.log('Fichiers:', req.files);

    // Prepare data according to the new English model
    const applicationData = {
      // PERSONAL INFORMATION (Step 1)
      title: req.body.civilite === 'Mme' ? 'Mrs' : 'Mr',
      lastName: req.body.nom,
      marriedName: req.body.nomMarital,
      firstName: req.body.prenom,
      currentSituation: (() => {
        const situationMapping = {
          'en_activite': 'employed',
          'recherche_emploi': 'job_seeking',
          'creation_entreprise': 'starting_business'
        };
        return situationMapping[req.body.situationActuelle] || 'employed';
      })(),
      employerInformed: req.body.employeurInforme === 'oui' ? 'yes' : 'no',

      // Required photo (will be added with files)

      // Personal information
      dateOfBirth: req.body.dateNaissance ? new Date(req.body.dateNaissance) : undefined,
      cityOfBirth: req.body.villeNaissance,
      countryOfBirth: req.body.paysNaissance,
      nationality: req.body.nationalite,

      // Contact
      phone: req.body.telephone,
      mobilePhone: req.body.telPortable,
      email: req.body.email,

      // ACADEMIC BACKGROUND (Step 2)
      academicBackground: req.body.parcoursAcademique ? JSON.parse(req.body.parcoursAcademique).map(item => ({
        year: item.annee,
        degree: item.diplome,
        institution: item.etablissement,
        country: item.pays
      })) : [],

      // PROFESSIONAL EXPERIENCE (Step 3)
      yearsOfExperience: parseInt(req.body.nombreAnneesExperience) || 0,
      yearsOfResponsibility: parseInt(req.body.anneesResponsabilite) || 0,

      // CURRENT OR LAST POSITION
      currentPosition: req.body.fonctionOccupee,
      positionStartDate: req.body.dureePosteDebut,
      positionEndDate: req.body.dureePosteFin,
      numberOfSubordinates: parseInt(req.body.nombreSubordonnes) || 0,
      fullTime: req.body.tempsPlein === 'true',
      companyName: req.body.nomEntreprise,
      activitySector: req.body.secteurActivite,
      companyAddress: req.body.adresseEntreprise,
      companyPhone: req.body.telephoneEntreprise,
      positionDescription: req.body.descriptionPoste,

      // PREVIOUS EXPERIENCES (dynamic)
      previousExperiences: req.body.experiencesAnterieures ? JSON.parse(req.body.experiencesAnterieures).map(exp => ({
        position: exp.fonction,
        startDate: exp.dureeDebut,
        endDate: exp.dureeFin,
        numberOfSubordinates: parseInt(exp.nombreSubordonnes) || 0,
        fullTime: exp.tempsPlein === true,
        companyName: exp.nomEntreprise,
        activitySector: exp.secteurActivite,
        address: exp.adresse,
        phone: exp.telephone,
        description: exp.description
      })) : [],

      situationSatisfactory: req.body.situationSatisfaisante === 'oui' ? 'yes' : (req.body.situationSatisfaisante === 'non' ? 'no' : undefined),
      situationDetails: req.body.detailsSituation,

      // LANGUAGES (Step 4) - With value validation
      languages: (() => {
        try {
          const languesData = req.body.langues ? JSON.parse(req.body.langues) : {};

          // Map French values to English
          const levelMapping = {
            'courant': 'fluent',
            'bon': 'good',
            'moyen': 'average',
            'debutant': 'beginner'
          };

          const validLevels = ['fluent', 'good', 'average', 'beginner'];

          return {
            french: {
              spoken: validLevels.includes(levelMapping[languesData.francais?.parle]) ? levelMapping[languesData.francais.parle] : 'fluent',
              read: validLevels.includes(levelMapping[languesData.francais?.lu]) ? levelMapping[languesData.francais.lu] : 'fluent',
              written: validLevels.includes(levelMapping[languesData.francais?.ecrit]) ? levelMapping[languesData.francais.ecrit] : 'fluent',
              test: languesData.francais?.test || ''
            },
            english: {
              spoken: validLevels.includes(levelMapping[languesData.anglais?.parle]) ? levelMapping[languesData.anglais.parle] : 'average',
              read: validLevels.includes(levelMapping[languesData.anglais?.lu]) ? levelMapping[languesData.anglais.lu] : 'average',
              written: validLevels.includes(levelMapping[languesData.anglais?.ecrit]) ? levelMapping[languesData.anglais.ecrit] : 'average',
              test: languesData.anglais?.test || ''
            },
            otherLanguage: {
              name: languesData.autreLangue?.nom || '',
              spoken: validLevels.includes(levelMapping[languesData.autreLangue?.parle]) ? levelMapping[languesData.autreLangue.parle] : undefined,
              read: validLevels.includes(levelMapping[languesData.autreLangue?.lu]) ? levelMapping[languesData.autreLangue.lu] : undefined,
              written: validLevels.includes(levelMapping[languesData.autreLangue?.ecrit]) ? levelMapping[languesData.autreLangue.ecrit] : undefined,
              test: languesData.autreLangue?.test || ''
            }
          };
        } catch (e) {
          // Default values in case of parsing error
          return {
            french: { spoken: 'fluent', read: 'fluent', written: 'fluent', test: '' },
            english: { spoken: 'average', read: 'average', written: 'average', test: '' },
            otherLanguage: { name: '', spoken: undefined, read: undefined, written: undefined, test: '' }
          };
        }
      })(),
      foreignLanguageUsage: req.body.languesEtrangeresPro === 'oui' ? 'yes' : 'no',
      languageUsageDetails: req.body.contexteLangues,

      // OTHER ACTIVITIES & PROFESSIONAL PROJECT (Step 5)
      livedAbroad: req.body.vecuEtranger === 'oui' ? 'yes' : 'no',
      abroadDetails: req.body.detailsEtranger,
      passionsInterests: req.body.passionCentreInteret,

      professionalProject: req.body.projetProfessionnel,
      trainingExpectations: req.body.attentesFormation,

      appliedOtherPrograms: req.body.candidatAutresProgrammes === 'oui' ? 'yes' : 'no',
      otherPrograms: req.body.autresProgrammes,
      funding: (() => {
        const fundingMapping = {
          'personnel': 'personal',
          'employeur_total': 'employer_full',
          'employeur_partiel': 'employer_partial',
          'autre': 'other'
        };
        return fundingMapping[req.body.financement] || 'personal';
      })(),
      otherFunding: req.body.financementAutre,
      howDidYouKnow: (() => {
        const sourceMapping = {
          'site_internet': 'website',
          'publicite': 'advertising',
          'presse': 'press',
          'entreprise': 'company',
          'ami': 'friend',
          'recherche_internet': 'internet_search',
          'session_info': 'info_session',
          'ancien_eleve': 'alumni',
          'autre': 'other'
        };
        return sourceMapping[req.body.commentConnu];
      })(),
      howDidYouKnowOther: req.body.commentConnuAutre,

      // Programme info (valeurs par défaut)
      programInfo: {
        program: 'EMBA',
        intakeYear: new Date().getFullYear().toString(),
        intakeSemester: 'Fall',
        studyMode: 'part_time'
      },

      // Financement (requis par le modèle) - Mapping des valeurs
      financing: {
        fundingSource: (() => {
          const financementMapping = {
            'personnel': 'self_funded',
            'employeur_total': 'employer_sponsored',
            'employeur_partiel': 'employer_sponsored',
            'autre': 'mixed'
          };
          return financementMapping[req.body.financement] || 'self_funded';
        })()
      },

      // Statut de candidature
      applicationStatus: {
        status: 'submitted',
        submissionDate: new Date()
      },

      // Documents (structure sera mise à jour avec les fichiers)
      documents: {}
    };

    // Process uploaded files according to new structure
    if (req.files) {
      Object.keys(req.files).forEach(fieldName => {
        const file = req.files[fieldName][0];

        // Map frontend field names to backend field names
        const fieldMapping = {
          'cv': 'cv',
          'cin': 'idCard',
          'photo': 'photo',
          'diplomes': 'diplomas',
          'lettresRecommandation': 'recommendationLetters',
          'attestationExperience': 'experienceCertificate'
        };

        const backendFieldName = fieldMapping[fieldName] || fieldName;

        // Add file to documents structure
        applicationData.documents[backendFieldName] = {
          filename: file.filename,
          originalName: file.originalname,
          path: file.path,
          size: file.size,
          mimeType: file.mimetype,
          uploadDate: new Date()
        };

        // Special handling for photo (also in main photo field)
        if (fieldName === 'photo') {
          applicationData.photo = {
            filename: file.filename,
            path: file.path,
            uploadDate: new Date()
          };
        }
      });
    }

    // Check that all required documents are present (using backend field names)
    const requiredDocuments = ['cv', 'idCard', 'photo', 'diplomas', 'recommendationLetters'];
    const missingDocuments = requiredDocuments.filter(doc => !applicationData.documents[doc]);

    if (missingDocuments.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Missing required documents: ${missingDocuments.join(', ')}`,
        missingDocuments: missingDocuments
      });
    }

    // Créer la candidature
    const application = new Application(applicationData);
    await application.save();

    console.log('✅ Candidature créée avec succès:', application.applicationNumber);

    console.log('✅ Candidature soumise avec succès');

    // Réponse de succès
    const response = {
      success: true,
      message: 'Candidature soumise avec succès !',
      applicationNumber: application.applicationNumber,
      applicationId: application._id
    };



    res.status(201).json(response);

  } catch (error) {
    console.error('❌ Erreur lors de la soumission:', error);
    res.status(400).json({
      success: false,
      message: 'Erreur lors de la soumission de la candidature',
      error: error.message
    });
  }
});

// Route pour sauvegarder la signature manuscrite
router.post('/signature', async (req, res) => {
  try {
    const { applicationId, signatureData } = req.body;

    if (!applicationId || !signatureData) {
      return res.status(400).json({
        success: false,
        message: 'ID de candidature et données de signature requis'
      });
    }

    // Trouver la candidature
    const application = await Application.findById(applicationId);
    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Candidature non trouvée'
      });
    }

    // Sauvegarder la signature
    application.signature = {
      data: signatureData,
      timestamp: new Date(),
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      status: 'signed'
    };

    await application.save();

    console.log('✅ Signature sauvegardée pour:', application.applicationNumber);

    res.json({
      success: true,
      message: 'Signature sauvegardée avec succès',
      applicationNumber: application.applicationNumber
    });

  } catch (error) {
    console.error('❌ Erreur sauvegarde signature:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la sauvegarde de la signature'
    });
  }
});

router.post('/', async (req, res) => {
  try {
    const application = new Application(req.body);
    await application.save();
    res.status(201).json(application);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.put('/:id', async (req, res) => {
  try {
    const application = await Application.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!application) return res.status(404).json({ message: 'Application not found' });
    res.json(application);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/submit', async (req, res) => {
  try {
    const { submittedBy } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    if (!application.isComplete()) {
      return res.status(400).json({ message: 'Application is not complete' });
    }

    await application.submit(submittedBy);
    res.json({ message: 'Application submitted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Route pour changer le statut d'une candidature
router.patch('/:id/status', async (req, res) => {
  try {
    const { status, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    // Mettre à jour le statut
    application.applicationStatus.status = status;
    application.applicationStatus.statusHistory.push({
      status,
      changeDate: new Date(),
      reason,
      comments: reason
    });

    if (status === 'accepted') {
      application.applicationStatus.decisionDate = new Date();
      application.applicationStatus.decision = 'accepted';
    } else if (status === 'rejected') {
      application.applicationStatus.decisionDate = new Date();
      application.applicationStatus.decision = 'rejected';
      application.applicationStatus.decisionReason = reason;

      // Envoyer email de refus
      try {
        await sendRejectionEmail({
          firstName: application.firstName,
          lastName: application.lastName,
          email: application.email
        }, reason);
      } catch (emailError) {
        console.error('Erreur lors de l\'envoi de l\'email de refus:', emailError);
      }
    } else if (status === 'interview_scheduled') {
      // Envoyer email pour entretien
      try {
        await sendInterviewEmail({
          firstName: application.firstName,
          lastName: application.lastName,
          email: application.email
        });
      } catch (emailError) {
        console.error('Erreur lors de l\'envoi de l\'email d\'entretien:', emailError);
      }
    }

    await application.save();

    res.json({
      success: true,
      message: 'Statut mis à jour avec succès',
      application
    });
  } catch (error) {
    console.error('Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({ message: error.message });
  }
});

router.post('/:id/accept', async (req, res) => {
  try {
    const { acceptedBy, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.accept(acceptedBy, reason);
    res.json({ message: 'Application accepted successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/reject', async (req, res) => {
  try {
    const { rejectedBy, reason } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.reject(rejectedBy, reason);
    res.json({ message: 'Application rejected successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.post('/:id/schedule-interview', async (req, res) => {
  try {
    const { date, time, interviewers, scheduledBy } = req.body;
    const application = await Application.findById(req.params.id);

    if (!application) return res.status(404).json({ message: 'Application not found' });

    await application.scheduleInterview(date, time, interviewers, scheduledBy);
    res.json({ message: 'Interview scheduled successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

router.get('/statistics', async (req, res) => {
  try {
    const { intakeYear, program, status } = req.query;
    const filters = {};
    if (intakeYear) filters.intakeYear = intakeYear;
    if (program) filters.program = program;
    if (status) filters.status = status;

    const stats = await Application.getApplicationStatistics(filters);
    res.json(stats[0] || {});
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Route pour créer un compte étudiant à partir d'une candidature acceptée
router.post('/:id/create-student', async (req, res) => {
  try {
    const application = await Application.findById(req.params.id);

    if (!application) {
      return res.status(404).json({ message: 'Candidature non trouvée' });
    }

    if (application.applicationStatus.status !== 'accepted') {
      return res.status(400).json({ message: 'La candidature doit être acceptée pour créer un compte étudiant' });
    }

    // Vérifier si un compte étudiant existe déjà
    const existingUser = await User.findOne({ email: application.email });
    if (existingUser) {
      return res.status(400).json({ message: 'Un compte existe déjà pour cet email' });
    }

    // Générer un mot de passe temporaire (utiliser la date de naissance ou un ID)
    const tempPassword = application.dateOfBirth ?
      application.dateOfBirth.toISOString().split('T')[0].replace(/-/g, '') :
      'EMBA2024';

    // Créer l'utilisateur
    const user = new User({
      firstName: application.firstName,
      lastName: application.lastName,
      email: application.email,
      phone: application.phone,
      password: tempPassword, // Sera hashé automatiquement par le middleware
      role: 'student',
      isActive: true,
      profile: {
        dateOfBirth: application.dateOfBirth,
        nationality: application.nationality
      }
    });

    await user.save();
    console.log('✅ Utilisateur créé avec succès:', user._id);

    // Générer un numéro d'étudiant unique
    const currentYear = new Date().getFullYear();
    const prefix = `EMBA${currentYear}`;

    // Trouver le prochain numéro disponible
    let nextNumber = 1;
    let studentNumber;
    let exists = true;

    while (exists) {
      studentNumber = `${prefix}${String(nextNumber).padStart(3, '0')}`;
      const existingStudent = await Student.findOne({ studentNumber });
      if (!existingStudent) {
        exists = false;
      } else {
        nextNumber++;
      }

      // Sécurité : éviter une boucle infinie
      if (nextNumber > 999) {
        throw new Error('Limite de numéros d\'étudiants atteinte pour cette année');
      }
    }

    console.log('📝 Numéro d\'étudiant généré:', studentNumber);

    // Créer le profil étudiant
    const student = new Student({
      user: user._id,
      studentNumber,
      academicYear: `${currentYear}-${currentYear + 1}`,
      cohort: `EMBA ${currentYear}`,
      program: application.programInfo?.program || 'EMBA',
      specialization: application.programInfo?.specialization,
      enrollmentStatus: 'active',
      enrollmentDate: new Date(),
      expectedGraduationDate: new Date(currentYear + 2, 5, 30), // 2 ans plus tard
      academicRecord: {
        currentGPA: 0,
        cumulativeGPA: 0,
        creditsCompleted: 0,
        creditsRequired: 60, // Standard EMBA program credits
        academicStanding: 'good'
      },
      tuitionFee: {
        total: application.financing?.totalCost || 25000,
        currency: application.financing?.currency || 'TND',
        paymentPlan: 'installments'
      }
    });

    await student.save();
    console.log('🎓 Étudiant créé avec succès dans la collection students:', student._id);

    // Marquer la candidature comme traitée
    application.applicationStatus.studentAccountCreated = true;
    application.applicationStatus.createdStudentId = student._id;
    await application.save();

    // Envoyer un email avec les identifiants
    try {
      await sendStudentCredentials({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        studentNumber,
        temporaryPassword: tempPassword
      });
      console.log('✅ Email d\'identifiants envoyé à:', user.email);
    } catch (emailError) {
      console.error('❌ Erreur lors de l\'envoi de l\'email:', emailError);
    }

    res.json({
      success: true,
      message: 'Compte étudiant créé avec succès et email envoyé',
      studentNumber,
      email: user.email,
      temporaryPassword: tempPassword,
      studentId: student._id
    });

  } catch (error) {
    console.error('Erreur lors de la création du compte étudiant:', error);
    res.status(500).json({ message: error.message });
  }
});




// Route pour servir les documents des candidatures
router.get('/documents/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(__dirname, '../uploads/applications/', filename);

    // Vérifier si le fichier existe
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Document non trouvé'
      });
    }

    // Obtenir les informations du fichier
    const stats = fs.statSync(filePath);
    const ext = path.extname(filename).toLowerCase();

    // Définir le type MIME
    let mimeType = 'application/octet-stream';
    switch (ext) {
      case '.pdf':
        mimeType = 'application/pdf';
        break;
      case '.jpg':
      case '.jpeg':
        mimeType = 'image/jpeg';
        break;
      case '.png':
        mimeType = 'image/png';
        break;
      case '.gif':
        mimeType = 'image/gif';
        break;
      case '.doc':
        mimeType = 'application/msword';
        break;
      case '.docx':
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
    }

    // Définir les headers
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache 1 heure

    // Envoyer le fichier
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('❌ Erreur lors de la lecture du document:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la lecture du document'
    });
  }
});





module.exports = router;
