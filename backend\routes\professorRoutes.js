const express = require('express');
const router = express.Router();
const Professor = require('../models/Professor');
const { authenticate, authorize } = require('../middleware/auth');
const { createProfessorWithCredentials, updateProfessorSalary } = require('../services/professorService');

// GET /api/professors - Obtenir tous les professeurs (Temporairement accessible à tous)
router.get('/', authenticate, async (req, res) => {
  try {
    const { page = 1, limit = 10, department, employmentStatus } = req.query;
    const query = {};
    
    if (department) query.department = department;
    if (employmentStatus) query.employmentStatus = employmentStatus;
    
    const professors = await Professor.find(query)
      .populate('user', 'firstName lastName email phone profilePicture')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    const total = await Professor.countDocuments(query);
    
    res.json({
      professors,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/profile - Obtenir le profil du professeur connecté
router.get('/profile', authenticate, async (req, res) => {
  try {
    console.log(`🔍 Recherche du profil professeur pour l'utilisateur: ${req.user.id}`);
    console.log(`👤 Utilisateur connecté: ${req.user.email} (${req.user.role})`);

    const professor = await Professor.findOne({ user: req.user.id })
      .populate('user', '-password -resetPasswordToken -verificationToken');

    if (!professor) {
      console.log(`❌ Aucun profil professeur trouvé pour l'utilisateur ${req.user.id}`);

      // Vérifier si l'utilisateur a le bon rôle
      if (req.user.role !== 'professor') {
        return res.status(403).json({
          success: false,
          message: `Accès refusé. Rôle requis: professor, rôle actuel: ${req.user.role}`
        });
      }

      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé. Contactez l\'administration pour créer votre profil.'
      });
    }

    console.log(`✅ Profil professeur trouvé: ${professor.employeeNumber}`);

    res.json({
      success: true,
      professor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération du profil:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération du profil',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// GET /api/professors/:id - Obtenir un professeur par ID
router.get('/:id', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id)
      .populate('user', '-password -resetPasswordToken -verificationToken');
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/professors - Créer un nouveau professeur
router.post('/', async (req, res) => {
  try {
    const professor = new Professor(req.body);
    await professor.save();
    
    const populatedProfessor = await Professor.findById(professor._id)
      .populate('user', 'firstName lastName email');
    
    res.status(201).json(populatedProfessor);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// PUT /api/professors/:id - Mettre à jour un professeur
router.put('/:id', async (req, res) => {
  try {
    const professor = await Professor.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedBy: req.body.updatedBy },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// GET /api/professors/:id/courses - Obtenir les cours d'un professeur
router.get('/:id/courses', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    const courses = await professor.getCurrentCourses();
    res.json(courses);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id/teaching-load - Obtenir la charge d'enseignement
router.get('/:id/teaching-load', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    const teachingLoad = await professor.getTeachingLoad();
    res.json(teachingLoad);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// GET /api/professors/:id/profile - Obtenir le profil public
router.get('/:id/profile', async (req, res) => {
  try {
    const professor = await Professor.findById(req.params.id);
    
    if (!professor) {
      return res.status(404).json({ message: 'Professor not found' });
    }
    
    res.json(professor.getPublicProfile());
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// POST /api/professors/admin/create - Créer un professeur avec credentials (Admin seulement)
router.post('/admin/create', authenticate, authorize(['admin']), async (req, res) => {
  try {
    console.log('🎓 Admin crée un nouveau professeur');
    console.log('Données reçues:', req.body);

    const {
      firstName,
      lastName,
      email,
      phone,
      academicTitle,
      academicRank,
      department,
      employmentStatus,
      contractType,
      startDate,
      salary,
      yearsOfTeaching,
      teachingAreas,
      preferredTeachingMethods,
      maxHoursPerWeek,
      canTravelForTeaching,
      languages,
      technicalSkills,
      expertise,
      bio
    } = req.body;

    // Validation des champs requis
    if (!firstName || !lastName || !email || !academicRank || !department) {
      return res.status(400).json({
        success: false,
        message: 'Les champs firstName, lastName, email, academicRank et department sont requis'
      });
    }

    // Préparer les données du professeur
    const professorData = {
      firstName,
      lastName,
      email,
      phone,
      academicTitle,
      academicRank,
      department,
      employmentStatus,
      contractType,
      startDate,
      salary,
      yearsOfTeaching,
      teachingAreas,
      preferredTeachingMethods,
      maxHoursPerWeek,
      canTravelForTeaching,
      languages,
      technicalSkills,
      expertise,
      bio
    };

    // Créer le professeur avec credentials
    const result = await createProfessorWithCredentials(professorData, req.user.id);

    res.status(201).json(result);
  } catch (error) {
    console.error('❌ Erreur lors de la création du professeur:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de la création du professeur'
    });
  }
});

// PUT /api/professors/admin/:id/salary - Mettre à jour le salaire (Admin seulement)
router.put('/admin/:id/salary', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { amount, currency, paymentFrequency } = req.body;

    if (!amount || amount < 0) {
      return res.status(400).json({
        success: false,
        message: 'Le montant du salaire est requis et doit être positif'
      });
    }

    const professor = await updateProfessorSalary(
      req.params.id,
      { amount, currency, paymentFrequency },
      req.user.id
    );

    res.json({
      success: true,
      message: 'Salaire mis à jour avec succès',
      professor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du salaire:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de la mise à jour du salaire'
    });
  }
});

// PUT /api/professors/admin/:id/status - Changer le statut d'emploi (Admin seulement)
router.put('/admin/:id/status', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { employmentStatus, contractType, endDate } = req.body;

    const updateData = {
      updatedBy: req.user.id
    };

    if (employmentStatus) updateData.employmentStatus = employmentStatus;
    if (contractType) updateData.contractType = contractType;
    if (endDate) updateData.endDate = endDate;

    const professor = await Professor.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    ).populate('user', '-password');

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Professeur non trouvé'
      });
    }

    console.log(`📋 Statut mis à jour pour le professeur ${professor.employeeNumber}`);

    res.json({
      success: true,
      message: 'Statut mis à jour avec succès',
      professor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du statut:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de la mise à jour du statut'
    });
  }
});

// GET /api/professors/admin/stats - Statistiques des professeurs (Admin seulement)
router.get('/admin/stats', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const stats = await Professor.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          byDepartment: {
            $push: {
              department: '$department',
              employmentStatus: '$employmentStatus'
            }
          },
          byStatus: {
            $push: '$employmentStatus'
          },
          totalSalaryBudget: {
            $sum: '$salary.amount'
          }
        }
      }
    ]);

    const departmentStats = await Professor.aggregate([
      {
        $group: {
          _id: '$department',
          count: { $sum: 1 },
          avgSalary: { $avg: '$salary.amount' }
        }
      }
    ]);

    const statusStats = await Professor.aggregate([
      {
        $group: {
          _id: '$employmentStatus',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        total: stats[0]?.total || 0,
        totalSalaryBudget: stats[0]?.totalSalaryBudget || 0,
        byDepartment: departmentStats,
        byStatus: statusStats
      }
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
});

// GET /api/professors/dashboard/stats - Statistiques du dashboard professeur
router.get('/dashboard/stats', authenticate, async (req, res) => {
  try {
    const professor = await Professor.findOne({ user: req.user.id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    // Pour l'instant, retourner des données par défaut
    // TODO: Implémenter les vraies statistiques quand les modèles Course, etc. seront prêts
    const stats = {
      totalCourses: 0,
      totalStudents: 0,
      weeklyHours: professor.availability?.maxHoursPerWeek || 0,
      averageRating: professor.averageRating || 0
    };

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
});

// GET /api/professors/dashboard/upcoming-classes - Cours à venir
router.get('/dashboard/upcoming-classes', authenticate, async (req, res) => {
  try {
    const professor = await Professor.findOne({ user: req.user.id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    // Pour l'instant, retourner des données d'exemple
    // TODO: Implémenter les vraies données quand les modèles Course, Schedule seront prêts
    const upcomingClasses = [
      {
        courseName: 'Management Stratégique',
        time: 'Aujourd\'hui 14:00 - 16:00',
        room: 'A101',
        studentCount: 25,
        status: 'Confirmé'
      },
      {
        courseName: 'Leadership et Innovation',
        time: 'Demain 09:00 - 11:00',
        room: 'B205',
        studentCount: 30,
        status: 'Confirmé'
      }
    ];

    res.json({
      success: true,
      classes: upcomingClasses
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des cours:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des cours'
    });
  }
});

// PUT /api/professors/profile - Mettre à jour le profil du professeur
router.put('/profile', authenticate, async (req, res) => {
  try {
    const professor = await Professor.findOne({ user: req.user.id });

    if (!professor) {
      return res.status(404).json({
        success: false,
        message: 'Profil professeur non trouvé'
      });
    }

    const allowedUpdates = [
      'professionalProfile.bio',
      'professionalProfile.expertise',
      'professionalProfile.consultingAreas',
      'professionalProfile.linkedinUrl',
      'professionalProfile.personalWebsite',
      'skills.technicalSkills',
      'skills.languages',
      'teachingExperience.teachingAreas',
      'teachingExperience.preferredTeachingMethods',
      'availability.preferredDays',
      'availability.preferredTimeSlots',
      'availability.canTravelForTeaching',
      'officeHours'
    ];

    // Mettre à jour seulement les champs autorisés
    Object.keys(req.body).forEach(key => {
      if (allowedUpdates.includes(key)) {
        if (key.includes('.')) {
          const [parent, child] = key.split('.');
          if (!professor[parent]) professor[parent] = {};
          professor[parent][child] = req.body[key];
        } else {
          professor[key] = req.body[key];
        }
      }
    });

    professor.updatedBy = req.user.id;
    await professor.save();

    const updatedProfessor = await Professor.findById(professor._id)
      .populate('user', '-password -resetPasswordToken -verificationToken');

    res.json({
      success: true,
      message: 'Profil mis à jour avec succès',
      professor: updatedProfessor
    });
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du profil'
    });
  }
});

module.exports = router;
